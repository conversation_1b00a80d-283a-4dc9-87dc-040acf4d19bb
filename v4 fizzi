PS C:\Users\<USER>\OneDrive\Desktop\fizzi chess> python v4_fizzi_sustained_excellence.py
🚀 V4 FIZZI - SUSTAINED EXCELLENCE
============================================================
🎯 CHALLENGE: ACHIEVE AND MAINTAIN 90%+ ALL METRICS
   📊 Precision: 90.0%+ SUSTAINED
   📊 Recall: 90.0%+ SUSTAINED
   📊 mAP50: 90.0%+ SUSTAINED
   📊 mAP50-95: 90.0%+ SUSTAINED
💪 STABILITY-FOCUSED REVOLUTIONARY APPROACH
🚀 EXECUTING V4 FIZZI SUSTAINED EXCELLENCE
============================================================
🎯 CHALLENGE: ACHIEVE AND MAINTAIN 90%+ ALL METRICS

🎯 CREATING ULTRA-STABLE DATASET
==================================================
📊 Train: 36 | Val: 12 (stable split)
C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v4_fizzi_sustained_excellence.py:157: RuntimeWarning: invalid value encountered in power
  aug_image = np.power(aug_image / 255.0, 1.0 / gamma) * 255.0
C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v4_fizzi_sustained_excellence.py:169: RuntimeWarning: invalid value encountered in cast
  return np.clip(aug_image, 0, 255).astype(np.uint8)
✅ Generated 468 ultra-stable samples
✅ Step 1: Created 468 ultra-stable samples

🎯 Step 2: Stability-Focused Training

🎯 STABILITY-FOCUSED TRAINING
==================================================
🤖 Starting from: v3_fizzi_rapid_best.pt
📊 Stability Parameters:
  epochs: 200
  batch: 8
  lr0: 0.002
  patience: 50
  mosaic: 0.5
  mixup: 0.0
🎯 Starting stability-focused training...
New https://pypi.org/project/ultralytics/8.3.148 available  Update with 'pip install -U ultralytics'
WARNING 'label_smoothing' is deprecated and will be removed in in the future.
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)
engine\trainer: agnostic_nms=False, amp=True, augment=True, auto_augment=randaugment, batch=8, bgr=0.0, box=5.0, cache=False, cfg=None, classes=None, close_mosaic=50, cls=1.0, conf=None, copy_paste=0.0, copy_paste_mode=flip, cos_lr=True, cutmix=0.0, data=v4_fizzi_stable_dataset.yaml, degrees=1.0, deterministic=True, device=None, dfl=1.0, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=200, erasing=0.4, exist_ok=True, fliplr=0.3, flipud=0.0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0.005, hsv_s=0.2, hsv_v=0.1, imgsz=416, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.002, lrf=0.0001, mask_ratio=4, max_det=300, mixup=0.0, mode=train, model=v3_fizzi_rapid_best.pt, momentum=0.937, mosaic=0.5, multi_scale=False, name=sustained_excellence, nbs=64, nms=False, opset=None, optimize=False, optimizer=AdamW, overlap_mask=True, patience=50, perspective=0.0, plots=True, pose=12.0, pretrained=True, profile=False, project=v4_fizzi_stable, rect=True, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=v4_fizzi_stable\sustained_excellence, save_frames=False, save_json=False, save_period=10, save_txt=False, scale=0.98, seed=0, shear=0.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0.02, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=20, warmup_momentum=0.8, weight_decay=0.0001, workers=8, workspace=None

                   from  n    params  module                                       arguments
  0                  -1  1       464  ultralytics.nn.modules.conv.Conv             [3, 16, 3, 2]
  1                  -1  1      4672  ultralytics.nn.modules.conv.Conv             [16, 32, 3, 2]
  2                  -1  1      6640  ultralytics.nn.modules.block.C3k2            [32, 64, 1, False, 0.25]
  3                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]
  4                  -1  1     26080  ultralytics.nn.modules.block.C3k2            [64, 128, 1, False, 0.25]
  5                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]
  6                  -1  1     87040  ultralytics.nn.modules.block.C3k2            [128, 128, 1, True]
  7                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]
  8                  -1  1    346112  ultralytics.nn.modules.block.C3k2            [256, 256, 1, True]
  9                  -1  1    164608  ultralytics.nn.modules.block.SPPF            [256, 256, 5]
 10                  -1  1    249728  ultralytics.nn.modules.block.C2PSA           [256, 256, 1]
 11                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 12             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 13                  -1  1    111296  ultralytics.nn.modules.block.C3k2            [384, 128, 1, False]
 14                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 15             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 16                  -1  1     32096  ultralytics.nn.modules.block.C3k2            [256, 64, 1, False]
 17                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]
 18            [-1, 13]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 19                  -1  1     86720  ultralytics.nn.modules.block.C3k2            [192, 128, 1, False]
 20                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]
 21            [-1, 10]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 22                  -1  1    378880  ultralytics.nn.modules.block.C3k2            [384, 256, 1, True]
 23        [16, 19, 22]  1    433012  ultralytics.nn.modules.head.Detect           [12, [64, 128, 256]]
YOLO11n summary: 181 layers, 2,592,180 parameters, 2,592,164 gradients, 6.5 GFLOPs

Transferred 499/499 items from pretrained weights
Freezing layer 'model.23.dfl.conv.weight'
AMP: running Automatic Mixed Precision (AMP) checks...
AMP: checks passed 
train: Fast image access  (ping: 0.10.0 ms, read: 461.8283.4 MB/s, size: 92.1 KB)
train: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v4_fizzi_stable_dataset\train\labels... 468 images, 0 backgroun
train: New cache created: C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v4_fizzi_stable_dataset\train\labels.cache
albumentations: Blur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))
WARNING 'rect=True' is incompatible with DataLoader shuffle, setting shuffle=False
val: Fast image access  (ping: 0.00.0 ms, read: 700.3101.3 MB/s, size: 71.7 KB)
val: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v4_fizzi_stable_dataset\val\labels... 12 images, 0 backgrounds, 0
val: New cache created: C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v4_fizzi_stable_dataset\val\labels.cache
Plotting labels to v4_fizzi_stable\sustained_excellence\labels.jpg... 
optimizer: AdamW(lr=0.002, momentum=0.937) with parameter groups 81 weight(decay=0.0), 88 weight(decay=0.0001), 87 bias(decay=0.0)
Image sizes 416 train, 416 val
Using 8 dataloader workers
Logging results to v4_fizzi_stable\sustained_excellence
Starting training for 200 epochs...

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      1/200     0.541G      0.696      6.032     0.5785          4        416: 100%|██████████| 59/59 [00:07<00:00,  7.88it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00,  6.94i 
                   all         12         12     0.0625       0.25     0.0696     0.0507

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      2/200     0.572G     0.7222      6.213     0.6019          4        416: 100%|██████████| 59/59 [00:05<00:00, 10.18it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 15.75i 
                   all         12         12      0.616      0.283       0.29      0.215

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      3/200     0.574G      0.644      4.654     0.5822          3        416: 100%|██████████| 59/59 [00:05<00:00, 10.39it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 14.93i
                   all         12         12      0.209      0.688      0.345      0.282

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      4/200     0.574G     0.6178      4.671     0.5796          4        416: 100%|██████████| 59/59 [00:05<00:00, 10.83it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 14.31i
                   all         12         12      0.392      0.438        0.3      0.248

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      5/200     0.574G     0.6135      4.292     0.5758          4        416: 100%|██████████| 59/59 [00:05<00:00, 10.82it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 13.70i 
                   all         12         12      0.418      0.517      0.403       0.33

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      6/200     0.574G      0.596      4.161     0.5647          3        416: 100%|██████████| 59/59 [00:05<00:00, 10.98it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 14.93i 
                   all         12         12      0.315       0.65      0.443      0.378

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      7/200     0.574G     0.6001      4.001     0.5757          4        416: 100%|██████████| 59/59 [00:05<00:00, 11.02it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 16.67i
                   all         12         12      0.159      0.938      0.353      0.276
 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      8/200     0.574G     0.5662      3.976     0.5577          4        416: 100%|██████████| 59/59 [00:05<00:00, 11.74it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 15.09i
                   all         12         12      0.125      0.312      0.291       0.23

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      9/200     0.574G     0.5763      3.888     0.5684          3        416: 100%|██████████| 59/59 [00:05<00:00, 11.78it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 15.39i 
                   all         12         12      0.143      0.562      0.347      0.289

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     10/200     0.574G      0.551      3.836     0.5557          4        416: 100%|██████████| 59/59 [00:05<00:00, 11.72it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 14.04i
                   all         12         12      0.161      0.562      0.341      0.282

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     11/200     0.574G     0.5454      3.674     0.5592          3        416: 100%|██████████| 59/59 [00:05<00:00, 11.70it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 14.49i 
                   all         12         12      0.161      0.562      0.323      0.263

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     12/200     0.574G     0.5255      3.818     0.5601          4        416: 100%|██████████| 59/59 [00:04<00:00, 11.90it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 14.70i
                   all         12         12      0.149      0.453      0.291       0.24

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     13/200     0.574G     0.5584      3.771     0.5691          3        416: 100%|██████████| 59/59 [00:04<00:00, 11.81it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 15.63i 
                   all         12         12      0.173      0.626      0.387      0.317

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     14/200     0.574G     0.5059      3.622     0.5586          4        416: 100%|██████████| 59/59 [00:05<00:00, 11.67it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 14.49i
                   all         12         12      0.168      0.537      0.368      0.316

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     15/200     0.574G     0.5205      3.632     0.5505          4        416: 100%|██████████| 59/59 [00:05<00:00, 11.77it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 15.03i
                   all         12         12      0.142      0.474      0.316      0.262

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     16/200     0.574G     0.5181      3.551     0.5602          4        416: 100%|██████████| 59/59 [00:04<00:00, 11.98it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 12.39i 
                   all         12         12      0.158      0.525      0.289      0.246

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     17/200     0.574G     0.5037      3.536     0.5537          4        416: 100%|██████████| 59/59 [00:05<00:00, 11.77it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 13.70i 
                   all         12         12     0.0921      0.938      0.311      0.264

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     18/200     0.574G     0.5012      3.505      0.558          4        416: 100%|██████████| 59/59 [00:05<00:00, 11.14it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 13.10i 
                   all         12         12      0.244      0.358      0.318      0.287

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     19/200     0.574G     0.4968      3.481     0.5499          3        416: 100%|██████████| 59/59 [00:04<00:00, 12.10it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 14.80i 
                   all         12         12      0.237      0.398       0.33       0.28

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     20/200     0.574G     0.5168      3.449     0.5599          4        416: 100%|██████████| 59/59 [00:04<00:00, 11.96it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 14.37i 
                   all         12         12     0.0776          1      0.305      0.263

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     21/200     0.574G     0.4934      3.379     0.5604          4        416: 100%|██████████| 59/59 [00:04<00:00, 12.30it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 14.49i
                   all         12         12     0.0918      0.375       0.34      0.278

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     22/200     0.574G     0.4998      3.451     0.5597          4        416: 100%|██████████| 59/59 [00:04<00:00, 12.40it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 15.10i 
                   all         12         12      0.141      0.791      0.246        0.2

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     23/200     0.574G     0.4866      3.324     0.5472          4        416: 100%|██████████| 59/59 [00:04<00:00, 12.27it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 16.67i 
                   all         12         12      0.259      0.477       0.27      0.224

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     24/200     0.574G     0.4841      3.417     0.5572          4        416: 100%|██████████| 59/59 [00:04<00:00, 12.36it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 14.49i 
                   all         12         12      0.106      0.375      0.284      0.235

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     25/200     0.574G     0.5023      3.324     0.5531          4        416: 100%|██████████| 59/59 [00:04<00:00, 12.71it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00, 16.66i 
                   all         12         12     0.0894          1      0.254      0.218
