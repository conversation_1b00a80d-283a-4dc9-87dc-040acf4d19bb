PS C:\Users\<USER>\OneDrive\Desktop\fizzi chess> python v4_fizzi_fresh_start.py
🚀 V4 FIZZI - FRESH START EXCELLENCE
============================================================
🎯 CHALLENGE: ACHIEVE AND MAINTAIN 90%+ ALL METRICS
   📊 Precision: 90.0%+ SUSTAINED
   📊 Recall: 90.0%+ SUSTAINED
   📊 mAP50: 90.0%+ SUSTAINED
   📊 mAP50-95: 90.0%+ SUSTAINED
🆕 FRESH UNTRAINED YOLOv11n APPROACH
🚀 EXECUTING V4 FIZZI FRESH START EXCELLENCE
============================================================
🎯 CHALLENGE: ACHIEVE AND MAINTAIN 90%+ ALL METRICS
🆕 STRATEGY: FRESH UNTRAINED YOLOv11n MODEL

🎯 CREATING OPTIMIZED FRESH DATASET
==================================================
📊 Train: 38 | Val: 10 (fresh learning split)
✅ Generated 722 optimized samples for fresh learning
✅ Step 1: Created 722 optimized samples

🆕 Step 2: Fresh Progressive Training (300 epochs)

🆕 FRESH PROGRESSIVE TRAINING
==================================================
🤖 Starting from: yolo11n.pt (FRESH UNTRAINED)
📊 Progressive Parameters:
  epochs: 300
  batch: 16
  lr0: 0.01
  warmup_epochs: 30
  patience: 100
🆕 Starting fresh progressive training...
New https://pypi.org/project/ultralytics/8.3.148 available  Update with 'pip install -U ultralytics'
WARNING 'label_smoothing' is deprecated and will be removed in in the future.
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)
engine\trainer: agnostic_nms=False, amp=True, augment=True, auto_augment=randaugment, batch=16, bgr=0.0, box=7.5, cache=False, cfg=None, classes=None, close_mosaic=100, cls=0.5, conf=None, copy_paste=0.1, copy_paste_mode=flip, cos_lr=True, cutmix=0.0, data=v4_fizzi_fresh_dataset.yaml, degrees=10.0, deterministic=True, device=None, dfl=1.5, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=300, erasing=0.4, exist_ok=True, fliplr=0.5, flipud=0.0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, imgsz=416, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.01, lrf=0.001, mask_ratio=4, max_det=300, mixup=0.15, mode=train, model=yolo11n.pt, momentum=0.937, mosaic=1.0, multi_scale=True, name=progressive_excellence, nbs=64, nms=False, opset=None, optimize=False, optimizer=AdamW, overlap_mask=True, patience=100, perspective=0.0, plots=True, pose=12.0, pretrained=True, profile=False, project=v4_fizzi_fresh, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=v4_fizzi_fresh\progressive_excellence, save_frames=False, save_json=False, save_period=25, save_txt=False, scale=0.5, seed=0, shear=2.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0.1, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=30, warmup_momentum=0.8, weight_decay=0.0005, workers=8, workspace=None      
Overriding model.yaml nc=80 with nc=12

                   from  n    params  module                                       arguments
  0                  -1  1       464  ultralytics.nn.modules.conv.Conv             [3, 16, 3, 2]
  1                  -1  1      4672  ultralytics.nn.modules.conv.Conv             [16, 32, 3, 2]
  2                  -1  1      6640  ultralytics.nn.modules.block.C3k2            [32, 64, 1, False, 0.25]
  3                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]
  4                  -1  1     26080  ultralytics.nn.modules.block.C3k2            [64, 128, 1, False, 0.25]
  5                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]
  6                  -1  1     87040  ultralytics.nn.modules.block.C3k2            [128, 128, 1, True]
  7                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]
  8                  -1  1    346112  ultralytics.nn.modules.block.C3k2            [256, 256, 1, True]
  9                  -1  1    164608  ultralytics.nn.modules.block.SPPF            [256, 256, 5]
 10                  -1  1    249728  ultralytics.nn.modules.block.C2PSA           [256, 256, 1]
 11                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 12             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 13                  -1  1    111296  ultralytics.nn.modules.block.C3k2            [384, 128, 1, False]
 14                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 15             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 16                  -1  1     32096  ultralytics.nn.modules.block.C3k2            [256, 64, 1, False]
 17                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]
 18            [-1, 13]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 19                  -1  1     86720  ultralytics.nn.modules.block.C3k2            [192, 128, 1, False]
 20                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]
 21            [-1, 10]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 22                  -1  1    378880  ultralytics.nn.modules.block.C3k2            [384, 256, 1, True]
 23        [16, 19, 22]  1    433012  ultralytics.nn.modules.head.Detect           [12, [64, 128, 256]]
YOLO11n summary: 181 layers, 2,592,180 parameters, 2,592,164 gradients, 6.5 GFLOPs

Transferred 448/499 items from pretrained weights
Freezing layer 'model.23.dfl.conv.weight'
AMP: running Automatic Mixed Precision (AMP) checks...
AMP: checks passed 
train: Fast image access  (ping: 0.00.0 ms, read: 1037.8528.5 MB/s, size: 85.1 KB)
train: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v4_fizzi_fresh_dataset\train\labels... 722 images, 0 
train: New cache created: C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v4_fizzi_fresh_dataset\train\labels.cache
albumentations: Blur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))
val: Fast image access  (ping: 0.00.0 ms, read: 529.0155.2 MB/s, size: 63.8 KB)
val: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v4_fizzi_fresh_dataset\val\labels... 10 images, 0 backg
val: New cache created: C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v4_fizzi_fresh_dataset\val\labels.cache       
Plotting labels to v4_fizzi_fresh\progressive_excellence\labels.jpg... 
optimizer: AdamW(lr=0.01, momentum=0.937) with parameter groups 81 weight(decay=0.0), 88 weight(decay=0.0005), 87 bias(decay=0.0)
Image sizes 416 train, 416 val
Using 8 dataloader workers
Logging results to v4_fizzi_fresh\progressive_excellence
Starting training for 300 epochs...

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      1/300      2.74G      2.072      5.299      1.072          3        384: 100%|██████████| 46/46 [00:10<00:00,  4.48
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10          0          0          0          0

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      2/300      2.74G      1.742      3.847     0.9935          4        480: 100%|██████████| 46/46 [00:07<00:00,  5.84
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10          0          0          0          0

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      3/300      2.74G      1.757      3.316     0.9904          5        416: 100%|██████████| 46/46 [00:07<00:00,  5.93
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0183      0.625      0.191      0.109

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      4/300      2.74G      1.771       3.24     0.9854          2        544: 100%|██████████| 46/46 [00:07<00:00,  6.21
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.807      0.125      0.112     0.0719

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      5/300      2.75G      1.907      3.043      0.965          4        224: 100%|██████████| 46/46 [00:06<00:00,  6.78
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.662      0.125     0.0457     0.0241

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      6/300      2.75G      1.615      2.714     0.9597          5        480: 100%|██████████| 46/46 [00:06<00:00,  6.91
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10      0.929      0.109     0.0983     0.0683

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      7/300      2.75G      1.653      2.762     0.9861          3        480: 100%|██████████| 46/46 [00:06<00:00,  7.19
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0212       0.75      0.122     0.0856

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      8/300      2.75G      1.673      2.623      0.968          4        544: 100%|██████████| 46/46 [00:06<00:00,  6.96
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0258      0.875      0.148     0.0913

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      9/300      2.75G      1.723      2.681     0.9694          6        640: 100%|██████████| 46/46 [00:06<00:00,  7.08
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0153      0.562     0.0288     0.0147
