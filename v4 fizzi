PS C:\Users\<USER>\OneDrive\Desktop\fizzi chess> python train_targeted_model.py --dataset v4_fizzi_fresh_dataset.yaml --epochs 150 --patience 100
Python version: 3.11.4 (tags/v3.11.4:d2340ef, Jun  7 2023, 05:45:37) [MSC v.1934 64 bit (AMD64)]
PyTorch version: 2.5.1+cu121
CUDA available: True
CUDA device: NVIDIA GeForce RTX 3050 6GB Laptop GPU
GPU Memory: 6.44 GB
CUDA Version: 12.1
cuDNN Version: 90100
Training on: 0
GPU: NVIDIA GeForce RTX 3050 6GB Laptop GPU
GPU Memory: 6.44 GB

=== Training Targeted Model (max 150 epochs) ===
Dataset: v4_fizzi_fresh_dataset.yaml
Model: yolo11n.pt
Project directory: chess_board_detection\piece_detection\models\targeted_yolo
Using aggressive augmentation and optimized loss weights for problematic piece focus
New https://pypi.org/project/ultralytics/8.3.148 available  Update with 'pip install -U ultralytics'
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)
engine\trainer: agnostic_nms=False, amp=True, augment=True, auto_augment=randaugment, batch=16, bgr=0.0, box=7.5, cache=True, cfg=None, classes=None, close_mosaic=50, cls=0.5, conf=None, copy_paste=0.0, copy_paste_mode=flip, cos_lr=False, cutmix=0.0, data=v4_fizzi_fresh_dataset.yaml, degrees=15.0, deterministic=True, device=0, dfl=1.5, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=150, erasing=0.4, exist_ok=True, fliplr=0.5, flipud=0.0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, imgsz=416, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.01, lrf=0.01, mask_ratio=4, max_det=300, mixup=0.5, mode=train, model=yolo11n.pt, momentum=0.937, mosaic=1.0, multi_scale=False, name=targeted_20250603_153417, nbs=64, nms=False, opset=None, optimize=False, optimizer=auto, overlap_mask=True, patience=100, perspective=0.0005, plots=True, pose=12.0, pretrained=True, profile=False, project=chess_board_detection\piece_detection\models\targeted_yolo, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=chess_board_detection\piece_detection\models\targeted_yolo\targeted_20250603_153417, save_frames=False, save_json=False, save_period=10, save_txt=False, scale=0.5, seed=42, shear=2.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0.2, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=3.0, warmup_momentum=0.8, weight_decay=0.0005, workers=8, workspace=None
Overriding model.yaml nc=80 with nc=12

                   from  n    params  module                                       arguments
  0                  -1  1       464  ultralytics.nn.modules.conv.Conv             [3, 16, 3, 2]
  1                  -1  1      4672  ultralytics.nn.modules.conv.Conv             [16, 32, 3, 2]
  2                  -1  1      6640  ultralytics.nn.modules.block.C3k2            [32, 64, 1, False, 0.25]
  3                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]
  4                  -1  1     26080  ultralytics.nn.modules.block.C3k2            [64, 128, 1, False, 0.25]
  5                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]
  6                  -1  1     87040  ultralytics.nn.modules.block.C3k2            [128, 128, 1, True]
  7                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]
  8                  -1  1    346112  ultralytics.nn.modules.block.C3k2            [256, 256, 1, True]
  9                  -1  1    164608  ultralytics.nn.modules.block.SPPF            [256, 256, 5]
 10                  -1  1    249728  ultralytics.nn.modules.block.C2PSA           [256, 256, 1]
 11                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 12             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 13                  -1  1    111296  ultralytics.nn.modules.block.C3k2            [384, 128, 1, False]
 14                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 15             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 16                  -1  1     32096  ultralytics.nn.modules.block.C3k2            [256, 64, 1, False]
 17                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]
 18            [-1, 13]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 19                  -1  1     86720  ultralytics.nn.modules.block.C3k2            [192, 128, 1, False]
 20                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]
 21            [-1, 10]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 22                  -1  1    378880  ultralytics.nn.modules.block.C3k2            [384, 256, 1, True]
 23        [16, 19, 22]  1    433012  ultralytics.nn.modules.head.Detect           [12, [64, 128, 256]]
YOLO11n summary: 181 layers, 2,592,180 parameters, 2,592,164 gradients, 6.5 GFLOPs

Transferred 448/499 items from pretrained weights
Freezing layer 'model.23.dfl.conv.weight'
AMP: running Automatic Mixed Precision (AMP) checks...
AMP: checks passed 
train: Fast image access  (ping: 0.00.0 ms, read: 818.7269.4 MB/s, size: 89.7 KB)
train: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v4_fizzi_fresh_dataset\train\labels.cache... 722 imag
WARNING cache='ram' may produce non-deterministic training results. Consider cache='disk' as a deterministic alternative if your disk space allows.
train: Caching images (0.3GB RAM): 100%|██████████| 722/722 [00:00<00:00, 3286.88it/s]
albumentations: Blur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))
val: Fast image access  (ping: 0.10.2 ms, read: 414.3333.1 MB/s, size: 71.2 KB)
val: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v4_fizzi_fresh_dataset\val\labels.cache... 10 images, 0
WARNING cache='ram' may produce non-deterministic training results. Consider cache='disk' as a deterministic alternative if your disk space allows.
val: Caching images (0.0GB RAM): 100%|██████████| 10/10 [00:00<00:00, 1429.02it/s]
Plotting labels to chess_board_detection\piece_detection\models\targeted_yolo\targeted_20250603_153417\labels.jpg... 
optimizer: 'optimizer=auto' found, ignoring 'lr0=0.01' and 'momentum=0.937' and determining best 'optimizer', 'lr0' and 'momentum' automatically...
optimizer: AdamW(lr=0.000625, momentum=0.9) with parameter groups 81 weight(decay=0.0), 88 weight(decay=0.0005), 87 bias(decay=0.0)
Image sizes 416 train, 416 val
Using 8 dataloader workers
Logging results to chess_board_detection\piece_detection\models\targeted_yolo\targeted_20250603_153417
Starting training for 150 epochs...

Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      1/150         1G       2.12      5.886        1.2          6        416: 100%|██████████| 46/46 [00:10<00:00,  4.40
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10          0          0          0          0

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      2/150      1.01G      1.639      4.858     0.9598          5        416: 100%|██████████| 46/46 [00:05<00:00,  8.46
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10    0.00134       0.25     0.0122    0.00844

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      3/150      1.01G      1.628      4.311     0.9491          4        416: 100%|██████████| 46/46 [00:05<00:00,  8.66
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10    0.00457      0.875     0.0607     0.0394

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      4/150      1.01G      1.498      3.889     0.9306          3        416: 100%|██████████| 46/46 [00:05<00:00,  9.00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10    0.00701      0.875      0.055     0.0333
                   