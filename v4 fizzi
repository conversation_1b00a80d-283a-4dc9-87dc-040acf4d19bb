PS C:\Users\<USER>\OneDrive\Desktop\fizzi chess> python v4_fizzi_fresh_start.py
🚀 V4 FIZZI - FRESH START EXCELLENCE
============================================================
🎯 CHALLENGE: ACHIEVE AND MAINTAIN 90%+ ALL METRICS
   📊 Precision: 90.0%+ SUSTAINED
   📊 Recall: 90.0%+ SUSTAINED
   📊 mAP50: 90.0%+ SUSTAINED
   📊 mAP50-95: 90.0%+ SUSTAINED
🆕 FRESH UNTRAINED YOLOv11n APPROACH
🚀 EXECUTING V4 FIZZI FRESH START EXCELLENCE
============================================================
🎯 CHALLENGE: ACHIEVE AND MAINTAIN 90%+ ALL METRICS
🆕 STRATEGY: FRESH UNTRAINED YOLOv11n MODEL

🎯 CREATING OPTIMIZED FRESH DATASET
==================================================
📊 Train: 38 | Val: 10 (fresh learning split)
✅ Generated 722 optimized samples for fresh learning
✅ Step 1: Created 722 optimized samples

🆕 Step 2: Fresh Progressive Training (300 epochs)

🆕 FRESH PROGRESSIVE TRAINING
==================================================
🤖 Starting from: yolo11n.pt (FRESH UNTRAINED)
📊 Progressive Parameters:
  epochs: 300
  batch: 16
  lr0: 0.01
  warmup_epochs: 30
  patience: 100
🆕 Starting fresh progressive training...
New https://pypi.org/project/ultralytics/8.3.148 available  Update with 'pip install -U ultralytics'
WARNING 'label_smoothing' is deprecated and will be removed in in the future.
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)
engine\trainer: agnostic_nms=False, amp=True, augment=True, auto_augment=randaugment, batch=16, bgr=0.0, box=7.5, cache=False, cfg=None, classes=None, close_mosaic=100, cls=0.5, conf=None, copy_paste=0.1, copy_paste_mode=flip, cos_lr=True, cutmix=0.0, data=v4_fizzi_fresh_dataset.yaml, degrees=10.0, deterministic=True, device=None, dfl=1.5, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=300, erasing=0.4, exist_ok=True, fliplr=0.5, flipud=0.0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, imgsz=416, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.01, lrf=0.001, mask_ratio=4, max_det=300, mixup=0.15, mode=train, model=yolo11n.pt, momentum=0.937, mosaic=1.0, multi_scale=True, name=progressive_excellence, nbs=64, nms=False, opset=None, optimize=False, optimizer=AdamW, overlap_mask=True, patience=100, perspective=0.0, plots=True, pose=12.0, pretrained=True, profile=False, project=v4_fizzi_fresh, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=v4_fizzi_fresh\progressive_excellence, save_frames=False, save_json=False, save_period=25, save_txt=False, scale=0.5, seed=0, shear=2.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0.1, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=30, warmup_momentum=0.8, weight_decay=0.0005, workers=8, workspace=None      
Overriding model.yaml nc=80 with nc=12

                   from  n    params  module                                       arguments
  0                  -1  1       464  ultralytics.nn.modules.conv.Conv             [3, 16, 3, 2]
  1                  -1  1      4672  ultralytics.nn.modules.conv.Conv             [16, 32, 3, 2]
  2                  -1  1      6640  ultralytics.nn.modules.block.C3k2            [32, 64, 1, False, 0.25]
  3                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]
  4                  -1  1     26080  ultralytics.nn.modules.block.C3k2            [64, 128, 1, False, 0.25]
  5                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]
  6                  -1  1     87040  ultralytics.nn.modules.block.C3k2            [128, 128, 1, True]
  7                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]
  8                  -1  1    346112  ultralytics.nn.modules.block.C3k2            [256, 256, 1, True]
  9                  -1  1    164608  ultralytics.nn.modules.block.SPPF            [256, 256, 5]
 10                  -1  1    249728  ultralytics.nn.modules.block.C2PSA           [256, 256, 1]
 11                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 12             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 13                  -1  1    111296  ultralytics.nn.modules.block.C3k2            [384, 128, 1, False]
 14                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 15             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 16                  -1  1     32096  ultralytics.nn.modules.block.C3k2            [256, 64, 1, False]
 17                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]
 18            [-1, 13]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 19                  -1  1     86720  ultralytics.nn.modules.block.C3k2            [192, 128, 1, False]
 20                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]
 21            [-1, 10]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 22                  -1  1    378880  ultralytics.nn.modules.block.C3k2            [384, 256, 1, True]
 23        [16, 19, 22]  1    433012  ultralytics.nn.modules.head.Detect           [12, [64, 128, 256]]
YOLO11n summary: 181 layers, 2,592,180 parameters, 2,592,164 gradients, 6.5 GFLOPs

Transferred 448/499 items from pretrained weights
Freezing layer 'model.23.dfl.conv.weight'
AMP: running Automatic Mixed Precision (AMP) checks...
AMP: checks passed 
train: Fast image access  (ping: 0.00.0 ms, read: 1037.8528.5 MB/s, size: 85.1 KB)
train: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v4_fizzi_fresh_dataset\train\labels... 722 images, 0 
train: New cache created: C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v4_fizzi_fresh_dataset\train\labels.cache
albumentations: Blur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))
val: Fast image access  (ping: 0.00.0 ms, read: 529.0155.2 MB/s, size: 63.8 KB)
val: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v4_fizzi_fresh_dataset\val\labels... 10 images, 0 backg
val: New cache created: C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v4_fizzi_fresh_dataset\val\labels.cache       
Plotting labels to v4_fizzi_fresh\progressive_excellence\labels.jpg... 
optimizer: AdamW(lr=0.01, momentum=0.937) with parameter groups 81 weight(decay=0.0), 88 weight(decay=0.0005), 87 bias(decay=0.0)
Image sizes 416 train, 416 val
Using 8 dataloader workers
Logging results to v4_fizzi_fresh\progressive_excellence
Starting training for 300 epochs...

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      1/300      2.74G      2.072      5.299      1.072          3        384: 100%|██████████| 46/46 [00:10<00:00,  4.48
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10          0          0          0          0

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      2/300      2.74G      1.742      3.847     0.9935          4        480: 100%|██████████| 46/46 [00:07<00:00,  5.84
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10          0          0          0          0

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      3/300      2.74G      1.757      3.316     0.9904          5        416: 100%|██████████| 46/46 [00:07<00:00,  5.93
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0183      0.625      0.191      0.109

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      4/300      2.74G      1.771       3.24     0.9854          2        544: 100%|██████████| 46/46 [00:07<00:00,  6.21
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.807      0.125      0.112     0.0719

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      5/300      2.75G      1.907      3.043      0.965          4        224: 100%|██████████| 46/46 [00:06<00:00,  6.78
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.662      0.125     0.0457     0.0241

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      6/300      2.75G      1.615      2.714     0.9597          5        480: 100%|██████████| 46/46 [00:06<00:00,  6.91
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10      0.929      0.109     0.0983     0.0683

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      7/300      2.75G      1.653      2.762     0.9861          3        480: 100%|██████████| 46/46 [00:06<00:00,  7.19
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0212       0.75      0.122     0.0856

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      8/300      2.75G      1.673      2.623      0.968          4        544: 100%|██████████| 46/46 [00:06<00:00,  6.96
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0258      0.875      0.148     0.0913

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      9/300      2.75G      1.723      2.681     0.9694          6        640: 100%|██████████| 46/46 [00:06<00:00,  7.08
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0153      0.562     0.0288     0.0147

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     10/300      2.75G      1.701      2.643     0.9664          1        576: 100%|██████████| 46/46 [00:06<00:00,  7.08
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0117      0.375     0.0691     0.0388

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     11/300      2.75G      1.667      2.518     0.9623          3        416: 100%|██████████| 46/46 [00:06<00:00,  7.26
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0542      0.562      0.113     0.0739

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     12/300      2.75G      1.738      2.546     0.9578          1        256: 100%|██████████| 46/46 [00:06<00:00,  7.48
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0145        0.5      0.123     0.0822

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     13/300      2.75G      1.603      2.447     0.9671          4        448: 100%|██████████| 46/46 [00:06<00:00,  6.95
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0126      0.688      0.105     0.0799

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     14/300      2.75G      1.696      2.556     0.9569          2        352: 100%|██████████| 46/46 [00:06<00:00,  6.72
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.782      0.125      0.145     0.0889

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     15/300      2.75G      1.631      2.434     0.9969          2        288: 100%|██████████| 46/46 [00:07<00:00,  6.23
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10      0.028      0.625      0.153      0.113

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     16/300      2.75G      1.685      2.421      0.987          2        416: 100%|██████████| 46/46 [00:06<00:00,  7.49
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0434      0.938      0.208      0.126

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     17/300      2.75G      1.566      2.351      0.974          2        544: 100%|██████████| 46/46 [00:06<00:00,  7.07
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0242      0.625      0.118     0.0745

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     18/300      2.75G      1.631      2.401      0.943          5        224: 100%|██████████| 46/46 [00:06<00:00,  7.29
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.292      0.254       0.21      0.117
 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     19/300      2.75G      1.724      2.395     0.9519          3        576: 100%|██████████| 46/46 [00:06<00:00,  7.65
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10      0.291      0.209      0.136     0.0759

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     20/300      2.75G      1.599      2.267     0.9273          2        288: 100%|██████████| 46/46 [00:05<00:00,  8.13
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.181      0.318      0.113     0.0698

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     21/300      2.75G      1.587      2.201     0.9374          2        288: 100%|██████████| 46/46 [00:05<00:00,  7.93
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0114      0.875     0.0799     0.0564

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     22/300      2.75G      1.574      2.327     0.9612          1        512: 100%|██████████| 46/46 [00:06<00:00,  7.53
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0157       0.75      0.107     0.0607

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     23/300      2.75G      1.572       2.41     0.9292          2        352: 100%|██████████| 46/46 [00:06<00:00,  7.31
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0348       0.75      0.118     0.0619

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     24/300      2.75G        1.5      2.341     0.9271          4        384: 100%|██████████| 46/46 [00:06<00:00,  7.35
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10      0.304      0.278      0.177     0.0929

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     25/300      2.75G      1.592      2.318     0.9637          2        544: 100%|██████████| 46/46 [00:06<00:00,  7.55
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.477      0.412      0.194      0.119

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     26/300      2.75G      1.482      2.194     0.9265          1        224: 100%|██████████| 46/46 [00:05<00:00,  8.02
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.315      0.285      0.202      0.137

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     27/300      2.75G      1.454       2.15     0.9321          2        224: 100%|██████████| 46/46 [00:06<00:00,  7.37
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0589      0.438      0.116     0.0835

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     28/300      2.75G       1.52      2.107     0.9016          3        320: 100%|██████████| 46/46 [00:05<00:00,  8.34
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0219      0.688     0.0847     0.0633

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     29/300      2.75G      1.468      2.132     0.9162          6        576: 100%|██████████| 46/46 [00:05<00:00,  7.83
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0339       0.25      0.145     0.0804

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     30/300      2.75G      1.469      2.114     0.9264          6        576: 100%|██████████| 46/46 [00:05<00:00,  7.99
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0318       0.75      0.125     0.0855

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     31/300      2.75G      1.422      2.108     0.9255          3        384: 100%|██████████| 46/46 [00:06<00:00,  7.66
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0312      0.125     0.0415     0.0329

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     32/300      2.75G      1.523      2.137     0.9425          2        480: 100%|██████████| 46/46 [00:05<00:00,  7.79
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0345        0.5      0.101     0.0663

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     33/300      2.75G      1.431      2.063      0.929          2        480: 100%|██████████| 46/46 [00:06<00:00,  7.43
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0193        0.5       0.19       0.12

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     34/300      2.75G      1.494      2.029     0.9057          0        544: 100%|██████████| 46/46 [00:05<00:00,  8.17
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10      0.023      0.688     0.0771     0.0575

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     35/300      2.75G       1.47      2.133     0.9334          7        416: 100%|██████████| 46/46 [00:06<00:00,  7.13
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.059      0.438      0.142      0.103

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     36/300      2.75G      1.472      2.035     0.9285          3        544: 100%|██████████| 46/46 [00:05<00:00,  7.85
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0507      0.625      0.098     0.0729

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     37/300      2.75G      1.388      2.049     0.9114          4        352: 100%|██████████| 46/46 [00:06<00:00,  7.58
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0372      0.386      0.163      0.115

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     38/300      2.75G      1.489      2.148     0.9328          1        288: 100%|██████████| 46/46 [00:06<00:00,  7.32
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0444       0.75      0.116     0.0682

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     39/300      2.75G      1.463      2.071      0.933          4        608: 100%|██████████| 46/46 [00:06<00:00,  7.15
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0469        0.5      0.118     0.0895
 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     40/300      2.75G      1.415      2.041     0.9352          1        544: 100%|██████████| 46/46 [00:06<00:00,  7.25
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0468       0.75      0.138     0.0953

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     41/300      2.75G      1.485      2.067      0.925          2        352: 100%|██████████| 46/46 [00:06<00:00,  7.61
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0568      0.375      0.126     0.0878

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     42/300      2.75G      1.445      2.033      0.906          3        544: 100%|██████████| 46/46 [00:05<00:00,  7.77
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10      0.164      0.125     0.0836     0.0493

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     43/300      2.75G      1.356      1.963      0.931          4        480: 100%|██████████| 46/46 [00:06<00:00,  6.94
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0211      0.125     0.0814      0.053

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     44/300      2.75G      1.402      2.019     0.9244          3        384: 100%|██████████| 46/46 [00:06<00:00,  7.47
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0318      0.438     0.0725     0.0511

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     45/300      2.75G      1.364       2.02     0.9044          4        416: 100%|██████████| 46/46 [00:06<00:00,  7.35
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0345       0.75      0.126     0.0852

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     46/300      2.75G      1.425       1.94     0.9232          4        224: 100%|██████████| 46/46 [00:06<00:00,  7.32
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.186        0.5      0.127      0.084

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     47/300      2.75G      1.409       1.96     0.8891          2        384: 100%|██████████| 46/46 [00:05<00:00,  8.41
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0473      0.625      0.132     0.0915

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     48/300      2.75G      1.366      1.944     0.8944          5        288: 100%|██████████| 46/46 [00:05<00:00,  7.76
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0383      0.625      0.117     0.0837

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     49/300      2.75G      1.341      1.916     0.9126          2        224: 100%|██████████| 46/46 [00:05<00:00,  7.84
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.162      0.125      0.102     0.0776

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     50/300      2.75G      1.311      1.913     0.8935          2        384: 100%|██████████| 46/46 [00:05<00:00,  7.70
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0443       0.75      0.167      0.113

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     51/300      2.75G      1.341      1.956      0.888          1        448: 100%|██████████| 46/46 [00:05<00:00,  8.28
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.242      0.362      0.146     0.0959

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     52/300      2.75G      1.259      1.897     0.9055          4        512: 100%|██████████| 46/46 [00:06<00:00,  7.37
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0597       0.25     0.0887     0.0526

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     53/300      2.75G      1.305      1.914     0.9063          4        608: 100%|██████████| 46/46 [00:06<00:00,  7.30
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0506       0.22      0.192      0.142

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     54/300      2.75G      1.266      1.845     0.8929          7        448: 100%|██████████| 46/46 [00:05<00:00,  7.73
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10      0.165      0.125      0.142     0.0966

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     55/300      2.75G      1.326      1.899     0.8869          5        192: 100%|██████████| 46/46 [00:05<00:00,  7.71
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0588        0.5      0.111     0.0804

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     56/300      2.75G      1.279      1.844     0.9075          4        320: 100%|██████████| 46/46 [00:06<00:00,  7.51
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10      0.289      0.125      0.104     0.0815

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     57/300      2.75G      1.235      1.884     0.8986          2        352: 100%|██████████| 46/46 [00:06<00:00,  7.47
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.197       0.25      0.152     0.0774

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     58/300      2.75G      1.225      1.801     0.8867          5        384: 100%|██████████| 46/46 [00:05<00:00,  8.00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0587      0.625      0.171      0.133

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     59/300      2.75G       1.28      1.832     0.8886          4        608: 100%|██████████| 46/46 [00:05<00:00,  7.99
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0484       0.25      0.134     0.0953

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     60/300      2.75G      1.293      1.826      0.905          2        384: 100%|██████████| 46/46 [00:05<00:00,  8.39
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.362      0.338      0.145      0.116

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     61/300      2.75G      1.297      1.867     0.9011          3        512: 100%|██████████| 46/46 [00:05<00:00,  7.81
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0582      0.249     0.0706     0.0445

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     62/300      2.75G      1.225      1.824     0.9016          1        448: 100%|██████████| 46/46 [00:06<00:00,  7.17
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0398      0.625      0.104     0.0729

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     63/300      2.75G      1.236      1.767     0.9077          1        416: 100%|██████████| 46/46 [00:06<00:00,  7.48
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0374      0.375      0.082     0.0597

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     64/300      2.75G       1.24      1.791     0.8832          2        224: 100%|██████████| 46/46 [00:06<00:00,  7.52
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10      0.185       0.25      0.116       0.09

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     65/300      2.75G      1.268      1.819     0.8844          2        256: 100%|██████████| 46/46 [00:06<00:00,  7.39
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0518      0.812      0.188       0.12

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     66/300      2.75G      1.282      1.806     0.8973          3        448: 100%|██████████| 46/46 [00:06<00:00,  7.65
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10      0.215      0.361      0.146     0.0973

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     67/300      2.75G      1.261      1.782     0.8922          4        512: 100%|██████████| 46/46 [00:06<00:00,  7.43
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.222      0.346      0.164      0.116

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     68/300      2.75G       1.28      1.794      0.909          2        640: 100%|██████████| 46/46 [00:06<00:00,  7.50
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.186      0.188      0.132      0.087

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     69/300      2.75G      1.344      1.856     0.8985          6        224: 100%|██████████| 46/46 [00:06<00:00,  7.66
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.047      0.375     0.0788     0.0538

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     70/300      2.75G      1.251      1.823     0.8756          2        416: 100%|██████████| 46/46 [00:05<00:00,  8.21
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0441        0.5      0.103     0.0799

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     71/300      2.75G      1.224      1.762     0.8914          7        608: 100%|██████████| 46/46 [00:06<00:00,  7.02
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0621      0.375      0.104     0.0742

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     72/300      2.75G      1.201      1.744     0.8876          6        320: 100%|██████████| 46/46 [00:06<00:00,  7.64
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10       0.16     0.0625      0.088     0.0736

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     73/300      2.75G      1.255      1.781     0.9141          4        384: 100%|██████████| 46/46 [00:06<00:00,  7.15
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0563       0.25      0.113     0.0809

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     74/300      2.75G      1.186      1.711     0.8966          2        512: 100%|██████████| 46/46 [00:06<00:00,  7.12
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0545      0.375     0.0984     0.0651

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     75/300      2.75G      1.317      1.842     0.8948          2        608: 100%|██████████| 46/46 [00:06<00:00,  7.58
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0361        0.5     0.0803      0.055

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     76/300      2.75G      1.233      1.767     0.8906          5        512: 100%|██████████| 46/46 [00:05<00:00,  7.72
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0565       0.25     0.0829     0.0567

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     77/300      2.75G      1.165       1.69     0.8894          3        512: 100%|██████████| 46/46 [00:05<00:00,  7.84
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0406       0.25     0.0829     0.0613

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     78/300      2.75G      1.204      1.727     0.8906          3        192: 100%|██████████| 46/46 [00:06<00:00,  7.26
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0386      0.375     0.0802     0.0578

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     79/300      2.76G      1.253      1.735     0.8937          2        320: 100%|██████████| 46/46 [00:05<00:00,  7.71
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.181      0.232     0.0871     0.0605

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     80/300      2.76G       1.25      1.818     0.8913          5        448: 100%|██████████| 46/46 [00:05<00:00,  7.81
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0394        0.5      0.112     0.0785

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     81/300      2.76G      1.198      1.791     0.8752          1        576: 100%|██████████| 46/46 [00:05<00:00,  7.95
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.318       0.25      0.087     0.0513

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     82/300      2.76G      1.213      1.743     0.8845          2        608: 100%|██████████| 46/46 [00:05<00:00,  8.00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10     0.0375      0.125     0.0933      0.063

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     83/300      2.76G      1.134      1.721     0.8976          1        480: 100%|██████████| 46/46 [00:06<00:00,  6.78
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.032        0.5     0.0976     0.0644

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     84/300      2.76G      1.136      1.692     0.8715          6        512: 100%|██████████| 46/46 [00:05<00:00,  7.73
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0484        0.5     0.0829     0.0576

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     85/300      2.76G       1.15       1.74     0.8725          0        224: 100%|██████████| 46/46 [00:06<00:00,  7.41
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.049      0.375      0.101     0.0704

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     86/300      2.76G      1.171       1.71     0.8857          2        224: 100%|██████████| 46/46 [00:06<00:00,  7.46
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10       0.06      0.375      0.114     0.0888

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     87/300      2.76G      1.204      1.658     0.8917          3        256: 100%|██████████| 46/46 [00:05<00:00,  7.71
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.253      0.473      0.144      0.113

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     88/300      2.76G      1.245      1.843     0.8966          2        544: 100%|██████████| 46/46 [00:06<00:00,  7.29
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00: 
                   all         10         10      0.189      0.188     0.0906     0.0694

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     89/300      2.76G      1.162       1.73     0.8725          3        640: 100%|██████████| 46/46 [00:05<00:00,  7.97
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10      0.219      0.336      0.148      0.109

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     90/300      2.76G      1.206      1.708     0.8645          5        608: 100%|██████████| 46/46 [00:05<00:00,  8.01
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10     0.0483        0.5      0.104     0.0777

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     91/300      2.76G      1.159      1.682     0.8857          6        544: 100%|██████████| 46/46 [00:06<00:00,  7.47
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10      0.102      0.419      0.179      0.132

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     92/300      2.76G      1.207      1.732     0.8906          2        576: 100%|██████████| 46/46 [00:06<00:00,  7.55
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:
                   all         10         10      0.191       0.25      0.107     0.0694
