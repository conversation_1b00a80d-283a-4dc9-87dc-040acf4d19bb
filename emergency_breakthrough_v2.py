#!/usr/bin/env python3
"""
Emergency Breakthrough V2 - Achieve 99% mAP50
Current performance: 18-20% mAP50 - CRITICAL INTERVENTION NEEDED
"""

import os
import yaml
from ultralytics import YOLO
import torch

class EmergencyBreakthroughV2:
    def __init__(self):
        self.target_map50 = 0.99
        self.base_model_path = 'best.pt'
        
        print("🚨 EMERGENCY BREAKTHROUGH V2 ACTIVATED")
        print("📊 Current Performance: 18-20% mAP50")
        print("🎯 Target: 99% mAP50")
        print("⚡ CRITICAL INTERVENTION REQUIRED")
        
    def create_massive_augmented_dataset(self):
        """Create 10x larger dataset through extreme augmentation."""
        
        print("🔄 Creating MASSIVE augmented dataset...")
        
        import cv2
        import numpy as np
        from pathlib import Path
        
        source_dir = 'fizzi_416x416_dataset'
        target_dir = 'fizzi_massive_dataset'
        
        os.makedirs(f'{target_dir}/images', exist_ok=True)
        os.makedirs(f'{target_dir}/labels', exist_ok=True)
        
        # Extreme augmentation parameters
        augmentations = [
            {'brightness': 0.3, 'contrast': 0.3},
            {'brightness': -0.2, 'contrast': 0.2},
            {'rotation': 15, 'brightness': 0.1},
            {'rotation': -15, 'contrast': 0.2},
            {'blur': 3, 'brightness': 0.1},
            {'noise': 20, 'contrast': 0.1},
            {'gamma': 1.3, 'brightness': 0.1},
            {'gamma': 0.7, 'contrast': 0.2},
            {'hue_shift': 10, 'saturation': 0.2},
            {'hue_shift': -10, 'brightness': 0.1}
        ]
        
        image_files = [f for f in os.listdir(f'{source_dir}/images') if f.endswith('.jpg')]
        total_generated = 0
        
        for img_file in image_files:
            base_name = img_file.replace('.jpg', '')
            
            # Load original image and label
            img_path = f'{source_dir}/images/{img_file}'
            label_path = f'{source_dir}/labels/{base_name}.txt'
            
            image = cv2.imread(img_path)
            
            # Copy original
            cv2.imwrite(f'{target_dir}/images/{img_file}', image)
            if os.path.exists(label_path):
                import shutil
                shutil.copy2(label_path, f'{target_dir}/labels/{base_name}.txt')
            total_generated += 1
            
            # Generate 20 augmented versions per image
            for i, aug_params in enumerate(augmentations * 2):  # 20 versions
                aug_image = image.copy()
                
                # Apply augmentations
                if 'brightness' in aug_params:
                    aug_image = cv2.convertScaleAbs(aug_image, alpha=1.0, beta=aug_params['brightness']*50)
                
                if 'contrast' in aug_params:
                    aug_image = cv2.convertScaleAbs(aug_image, alpha=1.0+aug_params['contrast'], beta=0)
                
                if 'rotation' in aug_params:
                    h, w = aug_image.shape[:2]
                    center = (w//2, h//2)
                    matrix = cv2.getRotationMatrix2D(center, aug_params['rotation'], 1.0)
                    aug_image = cv2.warpAffine(aug_image, matrix, (w, h))
                
                if 'blur' in aug_params:
                    aug_image = cv2.GaussianBlur(aug_image, (aug_params['blur'], aug_params['blur']), 0)
                
                if 'noise' in aug_params:
                    noise = np.random.normal(0, aug_params['noise'], aug_image.shape).astype(np.uint8)
                    aug_image = cv2.add(aug_image, noise)
                
                # Save augmented image
                aug_name = f'{base_name}_aug_{i}.jpg'
                cv2.imwrite(f'{target_dir}/images/{aug_name}', aug_image)
                
                # Copy label (same bounding boxes)
                if os.path.exists(label_path):
                    shutil.copy2(label_path, f'{target_dir}/labels/{base_name}_aug_{i}.txt')
                
                total_generated += 1
        
        print(f"✅ Generated {total_generated} training samples")
        
        # Create dataset config
        config = {
            'path': os.path.abspath(target_dir),
            'train': 'images',
            'val': os.path.abspath('fizzi_416x416_dataset/images'),  # Original as validation
            'nc': 12,
            'names': [
                'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
                'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
            ]
        }
        
        with open('fizzi_massive_dataset.yaml', 'w') as f:
            yaml.dump(config, f)
        
        return 'fizzi_massive_dataset.yaml', total_generated
    
    def emergency_high_performance_training(self, dataset_yaml):
        """Emergency training with aggressive parameters for 99% mAP50."""
        
        print("🚀 EMERGENCY HIGH-PERFORMANCE TRAINING")
        
        # Start fresh with base model
        model = YOLO(self.base_model_path)
        
        # AGGRESSIVE training parameters
        emergency_args = {
            'data': dataset_yaml,
            'epochs': 300,           # Extended training
            'imgsz': 416,
            'batch': 8,              # Larger batch
            'lr0': 0.01,             # HIGH learning rate
            'lrf': 0.001,            # Final learning rate
            'weight_decay': 0.0005,
            'warmup_epochs': 10,
            'patience': 100,         # High patience
            'project': 'emergency_breakthrough',
            'name': 'high_performance',
            'exist_ok': True,
            'optimizer': 'AdamW',
            'cos_lr': True,
            'close_mosaic': 50,
            'amp': True,
            'augment': True,
            'multi_scale': True,
            'rect': False,
            'overlap_mask': True,
            'mask_ratio': 4,
            'dropout': 0.0,          # No dropout for max performance
            'label_smoothing': 0.0,  # No smoothing for max performance
            'box': 7.5,
            'cls': 0.5,
            'dfl': 1.5,
            'hsv_h': 0.015,
            'hsv_s': 0.7,
            'hsv_v': 0.4,
            'degrees': 10.0,         # Rotation augmentation
            'translate': 0.1,
            'scale': 0.9,
            'shear': 2.0,
            'perspective': 0.0,
            'flipud': 0.0,
            'fliplr': 0.5,
            'mosaic': 1.0,
            'mixup': 0.15,           # Enable mixup
            'copy_paste': 0.3,       # Enable copy-paste
            'verbose': True,
            'save_period': 25,
            'plots': False,
            'val': True,
            'freeze': None           # NO FREEZING - full adaptation
        }
        
        print("📊 Emergency Training Parameters:")
        key_params = ['epochs', 'batch', 'lr0', 'mixup', 'copy_paste', 'freeze']
        for key in key_params:
            print(f"  {key}: {emergency_args[key]}")
        
        try:
            print("🚀 Starting emergency training...")
            results = model.train(**emergency_args)
            
            model_path = 'emergency_breakthrough/high_performance/weights/best.pt'
            return model_path, results
            
        except Exception as e:
            print(f"❌ Emergency training failed: {e}")
            return None, None
    
    def ultra_aggressive_training(self, dataset_yaml):
        """Ultra-aggressive training - sacrifice everything for 99% mAP50."""
        
        print("⚡ ULTRA-AGGRESSIVE TRAINING - NO HOLDS BARRED")
        
        # Start from scratch with YOLOv11n
        model = YOLO('yolo11n.pt')  # Fresh model
        
        ultra_args = {
            'data': dataset_yaml,
            'epochs': 500,           # MAXIMUM epochs
            'imgsz': 416,
            'batch': 16,             # MAXIMUM batch size
            'lr0': 0.02,             # MAXIMUM learning rate
            'lrf': 0.0001,
            'weight_decay': 0.0001,
            'warmup_epochs': 20,
            'patience': 150,
            'project': 'emergency_breakthrough',
            'name': 'ultra_aggressive',
            'exist_ok': True,
            'optimizer': 'AdamW',
            'cos_lr': True,
            'close_mosaic': 100,
            'amp': True,
            'augment': True,
            'multi_scale': True,
            'rect': False,
            'overlap_mask': True,
            'mask_ratio': 4,
            'dropout': 0.0,
            'label_smoothing': 0.0,
            'box': 10.0,             # Higher box loss weight
            'cls': 1.0,              # Higher classification weight
            'dfl': 2.0,              # Higher DFL weight
            'hsv_h': 0.02,
            'hsv_s': 0.9,
            'hsv_v': 0.6,
            'degrees': 15.0,
            'translate': 0.2,
            'scale': 0.8,
            'shear': 5.0,
            'perspective': 0.001,
            'flipud': 0.0,
            'fliplr': 0.5,
            'mosaic': 1.0,
            'mixup': 0.3,            # Maximum mixup
            'copy_paste': 0.5,       # Maximum copy-paste
            'verbose': True,
            'save_period': 50,
            'plots': False,
            'val': True,
            'freeze': None
        }
        
        print("📊 Ultra-Aggressive Parameters:")
        key_params = ['epochs', 'batch', 'lr0', 'box', 'cls', 'mixup', 'copy_paste']
        for key in key_params:
            print(f"  {key}: {ultra_args[key]}")
        
        try:
            print("⚡ Starting ultra-aggressive training...")
            results = model.train(**ultra_args)
            
            model_path = 'emergency_breakthrough/ultra_aggressive/weights/best.pt'
            return model_path, results
            
        except Exception as e:
            print(f"❌ Ultra-aggressive training failed: {e}")
            return None, None
    
    def evaluate_emergency_models(self):
        """Evaluate emergency trained models."""
        
        print("📊 EVALUATING EMERGENCY MODELS")
        
        models_to_evaluate = [
            'emergency_breakthrough/high_performance/weights/best.pt',
            'emergency_breakthrough/ultra_aggressive/weights/best.pt'
        ]
        
        best_model = None
        best_map50 = 0
        
        for model_path in models_to_evaluate:
            if os.path.exists(model_path):
                try:
                    model = YOLO(model_path)
                    results = model.val(
                        data='fizzi_massive_dataset.yaml',
                        imgsz=416,
                        batch=1,
                        verbose=False,
                        plots=False
                    )
                    
                    map50 = results.box.map50 if hasattr(results.box, 'map50') else 0
                    
                    print(f"📊 {os.path.basename(model_path)}: mAP50 = {map50:.4f} ({map50*100:.2f}%)")
                    
                    if map50 > best_map50:
                        best_map50 = map50
                        best_model = model_path
                        
                except Exception as e:
                    print(f"❌ Error evaluating {model_path}: {e}")
        
        return best_model, best_map50
    
    def execute_emergency_protocol(self):
        """Execute emergency breakthrough protocol."""
        
        print("🚨 EXECUTING EMERGENCY BREAKTHROUGH PROTOCOL")
        print("=" * 60)
        
        # Step 1: Create massive dataset
        dataset_yaml, sample_count = self.create_massive_augmented_dataset()
        print(f"✅ Step 1: Created {sample_count} training samples")
        
        # Step 2: Emergency high-performance training
        print("\n🚀 Step 2: Emergency High-Performance Training")
        hp_model, hp_results = self.emergency_high_performance_training(dataset_yaml)
        
        # Step 3: Ultra-aggressive training
        print("\n⚡ Step 3: Ultra-Aggressive Training")
        ua_model, ua_results = self.ultra_aggressive_training(dataset_yaml)
        
        # Step 4: Evaluate and select best
        print("\n📊 Step 4: Final Evaluation")
        best_model, best_map50 = self.evaluate_emergency_models()
        
        if best_model and best_map50 >= self.target_map50:
            print(f"\n🎉 EMERGENCY BREAKTHROUGH SUCCESS!")
            print(f"🏆 Best Model: {best_model}")
            print(f"🎯 mAP50: {best_map50:.4f} ({best_map50*100:.2f}%)")
            print(f"✅ TARGET ACHIEVED: {best_map50*100:.2f}% >= 99%")
            
            # Copy to final location
            final_path = 'emergency_breakthrough_v2_success.pt'
            import shutil
            shutil.copy2(best_model, final_path)
            print(f"💾 Final model saved: {final_path}")
            
            return final_path, best_map50
        else:
            print(f"\n📈 Emergency protocol completed")
            print(f"🎯 Best mAP50: {best_map50*100:.2f}% / 99% target")
            print(f"📊 Continue with additional strategies needed")
            
            return best_model, best_map50

def main():
    """Execute emergency breakthrough."""
    
    emergency = EmergencyBreakthroughV2()
    
    try:
        final_model, final_map50 = emergency.execute_emergency_protocol()
        
        print(f"\n🚨 EMERGENCY BREAKTHROUGH COMPLETED")
        print(f"🏆 Final Model: {final_model}")
        print(f"🎯 Final mAP50: {final_map50*100:.2f}%")
        
        if final_map50 >= 0.99:
            print(f"🎉 99% mAP50 TARGET ACHIEVED! 🎉")
        else:
            print(f"📈 Continue optimization needed")
            
    except Exception as e:
        print(f"❌ Emergency protocol failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
