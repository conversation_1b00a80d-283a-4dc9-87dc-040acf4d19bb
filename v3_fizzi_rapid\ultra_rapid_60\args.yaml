task: detect
mode: train
model: yolo11n.pt
data: v3_fizzi_rapid_dataset.yaml
epochs: 60
time: null
patience: 20
batch: 32
imgsz: 416
save: true
save_period: 10
cache: false
device: null
workers: 8
project: v3_fizzi_rapid
name: ultra_rapid_60
exist_ok: true
pretrained: true
optimizer: AdamW
verbose: true
seed: 0
deterministic: true
single_cls: false
rect: false
cos_lr: true
close_mosaic: 15
resume: false
amp: true
fraction: 1.0
profile: false
freeze: null
multi_scale: true
overlap_mask: true
mask_ratio: 4
dropout: 0.0
val: true
split: val
save_json: false
conf: null
iou: 0.7
max_det: 300
half: false
dnn: false
plots: false
source: null
vid_stride: 1
stream_buffer: false
visualize: false
augment: true
agnostic_nms: false
classes: null
retina_masks: false
embed: null
show: false
save_frames: false
save_txt: false
save_conf: false
save_crop: false
show_labels: true
show_conf: true
show_boxes: true
line_width: null
format: torchscript
keras: false
optimize: false
int8: false
dynamic: false
simplify: true
opset: null
workspace: null
nms: false
lr0: 0.02
lrf: 0.0001
momentum: 0.937
weight_decay: 0.0001
warmup_epochs: 3
warmup_momentum: 0.8
warmup_bias_lr: 0.1
box: 10.0
cls: 1.0
dfl: 2.0
pose: 12.0
kobj: 1.0
nbs: 64
hsv_h: 0.02
hsv_s: 0.8
hsv_v: 0.5
degrees: 8.0
translate: 0.15
scale: 0.8
shear: 2.0
perspective: 0.0
flipud: 0.0
fliplr: 0.5
bgr: 0.0
mosaic: 1.0
mixup: 0.3
cutmix: 0.0
copy_paste: 0.3
copy_paste_mode: flip
auto_augment: randaugment
erasing: 0.4
cfg: null
tracker: botsort.yaml
save_dir: v3_fizzi_rapid\ultra_rapid_60
