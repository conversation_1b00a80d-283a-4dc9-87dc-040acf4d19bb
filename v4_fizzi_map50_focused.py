#!/usr/bin/env python3
"""
V4 FIZZI - mAP50 FOCUSED TRAINING
Target: ACHIEVE 90%+ mAP50 (other metrics will follow automatically)
Strategy: Use the EXACT configuration that achieved 97% mAP50

Key Success Factors from train_targeted_model.py:
- Class weights for problematic pieces
- Aggressive augmentation (mixup=0.5, mosaic=1.0)
- Optimal loss weights (box=7.5, cls=0.5, dfl=1.5)
- High learning rate (lr0=lrf for sustained learning)
"""

import os
import yaml
import shutil
from datetime import datetime
from ultralytics import YOLO

class V4FizziMAP50Focused:
    def __init__(self):
        self.target_map50 = 0.90  # 90% mAP50 target
        
        print("🚀 V4 FIZZI - mAP50 FOCUSED TRAINING")
        print("=" * 60)
        print("🎯 PRIMARY TARGET: 90%+ mAP50")
        print("📈 STRATEGY: Use 97% mAP50 successful configuration")
        print("🔥 FOCUS: mAP50 improvement → other metrics follow")
        
    def create_map50_optimized_dataset(self):
        """Create dataset optimized for mAP50 improvement."""
        
        print("\n🎯 CREATING mAP50 OPTIMIZED DATASET")
        print("=" * 50)
        
        source_dir = 'fizzi_416x416_dataset'
        target_dir = 'v4_fizzi_map50_dataset'
        
        os.makedirs(f'{target_dir}/train/images', exist_ok=True)
        os.makedirs(f'{target_dir}/train/labels', exist_ok=True)
        os.makedirs(f'{target_dir}/val/images', exist_ok=True)
        os.makedirs(f'{target_dir}/val/labels', exist_ok=True)
        
        # Copy all original data (no additional augmentation - let YOLO handle it)
        import glob
        
        # Get all images
        image_files = glob.glob(f'{source_dir}/images/*.jpg')
        
        # 85/15 split for maximum training data
        import random
        random.shuffle(image_files)
        split_idx = int(0.85 * len(image_files))
        train_images = image_files[:split_idx]
        val_images = image_files[split_idx:]
        
        print(f"📊 Train: {len(train_images)} | Val: {len(val_images)} (85/15 split)")
        
        # Copy training data
        for img_path in train_images:
            img_name = os.path.basename(img_path)
            base_name = img_name.replace('.jpg', '')
            
            # Copy image
            shutil.copy2(img_path, f'{target_dir}/train/images/{img_name}')
            
            # Copy label if exists
            label_path = f'{source_dir}/labels/{base_name}.txt'
            if os.path.exists(label_path):
                shutil.copy2(label_path, f'{target_dir}/train/labels/{base_name}.txt')
        
        # Copy validation data
        for img_path in val_images:
            img_name = os.path.basename(img_path)
            base_name = img_name.replace('.jpg', '')
            
            # Copy image
            shutil.copy2(img_path, f'{target_dir}/val/images/{img_name}')
            
            # Copy label if exists
            label_path = f'{source_dir}/labels/{base_name}.txt'
            if os.path.exists(label_path):
                shutil.copy2(label_path, f'{target_dir}/val/labels/{base_name}.txt')
        
        print(f"✅ Created mAP50-optimized dataset: {len(train_images)} train, {len(val_images)} val")
        
        # Dataset config
        config = {
            'path': os.path.abspath(target_dir),
            'train': 'train/images',
            'val': 'val/images',
            'nc': 12,
            'names': [
                'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
                'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
            ]
        }
        
        with open('v4_fizzi_map50_dataset.yaml', 'w') as f:
            yaml.dump(config, f)
        
        return 'v4_fizzi_map50_dataset.yaml', len(train_images) + len(val_images)
    
    def map50_breakthrough_training(self, dataset_yaml):
        """mAP50 breakthrough training using 97% successful configuration."""
        
        print("\n🔥 mAP50 BREAKTHROUGH TRAINING")
        print("=" * 50)
        
        # Use FRESH untrained YOLOv11n model
        base_model = 'yolo11n.pt'
        print(f"🤖 Starting from: {base_model} (FRESH UNTRAINED)")
        
        model = YOLO(base_model)
        
        # Create timestamp for unique run name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        run_name = f"map50_breakthrough_{timestamp}"
        
        # Class weights focusing on problematic pieces (from successful script)
        class_weights = {
            0: 1.0,  # white_pawn
            1: 2.0,  # white_knight - problematic
            2: 3.0,  # white_bishop - very problematic
            3: 1.0,  # white_rook
            4: 2.5,  # white_queen - problematic
            5: 1.0,  # white_king
            6: 1.0,  # black_pawn
            7: 1.0,  # black_knight
            8: 2.0,  # black_bishop - problematic
            9: 1.0,  # black_rook
            10: 2.5, # black_queen - problematic
            11: 1.0  # black_king
        }
        
        # Convert to string format for YOLO
        cls_weights_str = ",".join([f"{i}:{w}" for i, w in class_weights.items()])
        print(f"🎯 Class weights: {cls_weights_str}")
        
        # EXACT configuration from successful 97% mAP50 script
        breakthrough_args = {
            # Basic Configuration
            'data': dataset_yaml,
            'epochs': 200,           # Sufficient epochs for breakthrough
            'imgsz': 416,
            'batch': 16,
            'device': 0,
            'project': 'v4_fizzi_map50',
            'name': run_name,
            'exist_ok': True,
            'pretrained': True,
            'verbose': True,
            'seed': 42,
            'patience': 100,         # High patience for breakthrough
            
            # Optimization Settings (EXACT from successful script)
            'lr0': 0.01,
            'lrf': 0.01,             # Same as lr0 for sustained learning
            'momentum': 0.937,
            'weight_decay': 0.0005,
            'warmup_epochs': 3.0,
            'warmup_momentum': 0.8,
            'warmup_bias_lr': 0.1,
            
            # Augmentation Settings (EXACT from successful script)
            'augment': True,
            'mosaic': 1.0,           # Full mosaic
            'mixup': 0.5,            # High mixup for robustness
            'degrees': 15.0,         # Rotation
            'translate': 0.2,        # Translation
            'scale': 0.5,            # Scaling
            'shear': 2.0,            # Shearing
            'fliplr': 0.5,           # Horizontal flip
            'perspective': 0.0005,   # Perspective
            'hsv_h': 0.015,          # Hue
            'hsv_s': 0.7,            # Saturation
            'hsv_v': 0.4,            # Value
            'close_mosaic': 50,      # Late mosaic closure
            
            # Loss Function Weights (EXACT from successful script)
            'box': 7.5,              # High box loss weight
            'cls': 0.5,              # Lower classification weight
            'dfl': 1.5,              # Distribution focal loss
            
            # Class Weights for Problematic Pieces
            'cls_weights': cls_weights_str,
            
            # Advanced Features
            'amp': True,
            'cache': True,
            'iou': 0.7,
            'max_det': 300,
            
            # Validation and Saving
            'val': True,
            'save': True,
            'save_period': 25,
            'plots': True
        }
        
        print("📊 Breakthrough Parameters (97% mAP50 config):")
        key_params = ['epochs', 'lr0', 'lrf', 'mixup', 'mosaic', 'box', 'cls', 'dfl']
        for key in key_params:
            print(f"  {key}: {breakthrough_args[key]}")
        
        try:
            print("🔥 Starting mAP50 breakthrough training...")
            results = model.train(**breakthrough_args)
            
            model_path = f'v4_fizzi_map50/{run_name}/weights/best.pt'
            return model_path, results
            
        except Exception as e:
            print(f"❌ Breakthrough training failed: {e}")
            return None, None
    
    def evaluate_map50_performance(self, model_path, dataset_yaml):
        """Evaluate model focusing on mAP50 performance."""
        
        if not os.path.exists(model_path):
            return None
        
        try:
            model = YOLO(model_path)
            
            # Single comprehensive evaluation
            results = model.val(
                data=dataset_yaml,
                imgsz=416,
                batch=16,
                conf=0.25,
                iou=0.7,
                max_det=300,
                verbose=True,
                plots=True
            )
            
            metrics = {
                'precision': getattr(results.box, 'mp', 0),
                'recall': getattr(results.box, 'mr', 0),
                'map50': getattr(results.box, 'map50', 0),
                'map50_95': getattr(results.box, 'map', 0)
            }
            
            return metrics
            
        except Exception as e:
            print(f"❌ Error evaluating {model_path}: {e}")
            return None
    
    def execute_v4_map50_breakthrough(self):
        """Execute V4 Fizzi mAP50 breakthrough pipeline."""
        
        print("🚀 EXECUTING V4 FIZZI mAP50 BREAKTHROUGH")
        print("=" * 60)
        print("🎯 PRIMARY TARGET: 90%+ mAP50")
        print("🔥 STRATEGY: 97% mAP50 successful configuration")
        
        # Step 1: Create mAP50-optimized dataset
        dataset_yaml, sample_count = self.create_map50_optimized_dataset()
        print(f"✅ Step 1: Created {sample_count} samples for mAP50 optimization")
        
        # Step 2: mAP50 breakthrough training
        print("\n🔥 Step 2: mAP50 Breakthrough Training")
        model_path, results = self.map50_breakthrough_training(dataset_yaml)
        
        # Step 3: Evaluate mAP50 performance
        print("\n📊 Step 3: mAP50 Performance Evaluation")
        
        if model_path:
            metrics = self.evaluate_map50_performance(model_path, dataset_yaml)
            
            if metrics:
                print(f"\n🏆 V4 FIZZI mAP50 BREAKTHROUGH RESULTS")
                print("=" * 60)
                
                map50_achieved = metrics['map50'] >= self.target_map50
                
                print(f"📊 mAP50: {metrics['map50']*100:.1f}% (target: 90%)")
                print(f"📊 Precision: {metrics['precision']*100:.1f}%")
                print(f"📊 Recall: {metrics['recall']*100:.1f}%")
                print(f"📊 mAP50-95: {metrics['map50_95']*100:.1f}%")
                
                if map50_achieved:
                    print("🎉 mAP50 TARGET ACHIEVED! 90%+ mAP50 SUCCESS!")
                    final_path = 'v4_fizzi_map50_breakthrough_success.pt'
                else:
                    print(f"📈 mAP50 Progress: {metrics['map50']*100/90:.1f}% toward target")
                    final_path = 'v4_fizzi_map50_breakthrough_best.pt'
                
                shutil.copy2(model_path, final_path)
                print(f"💾 Final model saved: {final_path}")
                
                return final_path, metrics, map50_achieved
        
        return None, None, False

def main():
    """Execute V4 Fizzi mAP50 breakthrough."""
    
    breakthrough = V4FizziMAP50Focused()
    
    try:
        final_model, final_metrics, success = breakthrough.execute_v4_map50_breakthrough()
        
        print(f"\n🚀 V4 FIZZI mAP50 BREAKTHROUGH COMPLETED")
        print(f"🏆 Final Model: {final_model}")
        
        if success:
            print(f"🎉 mAP50 BREAKTHROUGH ACHIEVED! 90%+ mAP50 SUCCESS! 🎉")
        else:
            print(f"📈 Significant mAP50 progress using proven configuration")
            
    except Exception as e:
        print(f"❌ V4 mAP50 breakthrough failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
