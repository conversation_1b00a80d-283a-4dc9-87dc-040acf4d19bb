task: detect
mode: train
model: v3_fizzi_rapid_best.pt
data: v4_fizzi_stable_dataset.yaml
epochs: 200
time: null
patience: 50
batch: 8
imgsz: 416
save: true
save_period: 10
cache: false
device: null
workers: 8
project: v4_fizzi_stable
name: sustained_excellence
exist_ok: true
pretrained: true
optimizer: AdamW
verbose: true
seed: 0
deterministic: true
single_cls: false
rect: true
cos_lr: true
close_mosaic: 50
resume: false
amp: true
fraction: 1.0
profile: false
freeze: null
multi_scale: false
overlap_mask: true
mask_ratio: 4
dropout: 0.0
val: true
split: val
save_json: false
conf: null
iou: 0.7
max_det: 300
half: false
dnn: false
plots: true
source: null
vid_stride: 1
stream_buffer: false
visualize: false
augment: true
agnostic_nms: false
classes: null
retina_masks: false
embed: null
show: false
save_frames: false
save_txt: false
save_conf: false
save_crop: false
show_labels: true
show_conf: true
show_boxes: true
line_width: null
format: torchscript
keras: false
optimize: false
int8: false
dynamic: false
simplify: true
opset: null
workspace: null
nms: false
lr0: 0.002
lrf: 0.0001
momentum: 0.937
weight_decay: 0.0001
warmup_epochs: 20
warmup_momentum: 0.8
warmup_bias_lr: 0.1
box: 5.0
cls: 1.0
dfl: 1.0
pose: 12.0
kobj: 1.0
nbs: 64
hsv_h: 0.005
hsv_s: 0.2
hsv_v: 0.1
degrees: 1.0
translate: 0.02
scale: 0.98
shear: 0.0
perspective: 0.0
flipud: 0.0
fliplr: 0.3
bgr: 0.0
mosaic: 0.5
mixup: 0.0
cutmix: 0.0
copy_paste: 0.0
copy_paste_mode: flip
auto_augment: randaugment
erasing: 0.4
cfg: null
tracker: botsort.yaml
save_dir: v4_fizzi_stable\sustained_excellence
