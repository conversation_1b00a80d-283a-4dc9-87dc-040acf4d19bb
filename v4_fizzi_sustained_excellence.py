#!/usr/bin/env python3
"""
V4 FIZZI - SUSTAINED EXCELLENCE
Target: ACHIEVE AND MAINTAIN 90%+ on ALL metrics consistently
Challenge: Not just reaching targets, but sustaining them throughout training

Based on V3 analysis:
- V3 achieved 92.4% precision, 92.9% recall, 48.3% mAP50 peaks
- Problem: Extreme volatility, couldn't maintain high performance
- Solution: Stability-focused training with consistency enforcement
"""

import os
import yaml
import cv2
import numpy as np
import random
import shutil
from ultralytics import YOLO

class V4FizziSustainedExcellence:
    def __init__(self):
        self.targets = {
            'precision': 0.90,    # 90%+ precision - SUSTAINED
            'recall': 0.90,       # 90%+ recall - SUSTAINED
            'map50': 0.90,        # 90%+ mAP50 - SUSTAINED
            'map50_95': 0.90      # 90%+ mAP50-95 - SUSTAINED
        }
        
        # Consistency tracking
        self.consistency_threshold = 5  # Must maintain for 5+ consecutive epochs
        self.target_epochs = []  # Track epochs meeting all targets
        
        print("🚀 V4 FIZZI - SUSTAINED EXCELLENCE")
        print("=" * 60)
        print("🎯 CHALLENGE: ACHIEVE AND MAINTAIN 90%+ ALL METRICS")
        print(f"   📊 Precision: {self.targets['precision']*100}%+ SUSTAINED")
        print(f"   📊 Recall: {self.targets['recall']*100}%+ SUSTAINED") 
        print(f"   📊 mAP50: {self.targets['map50']*100}%+ SUSTAINED")
        print(f"   📊 mAP50-95: {self.targets['map50_95']*100}%+ SUSTAINED")
        print("💪 STABILITY-FOCUSED REVOLUTIONARY APPROACH")
        
    def create_ultra_stable_dataset(self):
        """Create ultra-stable dataset for consistent performance."""
        
        print("\n🎯 CREATING ULTRA-STABLE DATASET")
        print("=" * 50)
        
        source_dir = 'fizzi_416x416_dataset'
        target_dir = 'v4_fizzi_stable_dataset'
        
        os.makedirs(f'{target_dir}/train/images', exist_ok=True)
        os.makedirs(f'{target_dir}/train/labels', exist_ok=True)
        os.makedirs(f'{target_dir}/val/images', exist_ok=True)
        os.makedirs(f'{target_dir}/val/labels', exist_ok=True)
        
        # Ultra-stable augmentations (minimal, high-quality)
        stable_augmentations = [
            # Minimal brightness variations
            {'brightness': 0.02, 'contrast': 0.01},
            {'brightness': -0.02, 'contrast': 0.01},
            {'brightness': 0.01, 'contrast': 0.02},
            {'brightness': -0.01, 'contrast': -0.01},
            # Tiny rotations for robustness
            {'rotation': 0.5, 'brightness': 0.005},
            {'rotation': -0.5, 'brightness': -0.005},
            # Minimal noise for generalization
            {'noise': 1, 'brightness': 0.002},
            {'gamma': 1.01, 'contrast': 0.005},
            {'gamma': 0.99, 'contrast': -0.005},
            # Perfect combinations for stability
            {'brightness': 0.005, 'contrast': 0.005, 'gamma': 1.005},
            {'brightness': -0.005, 'contrast': -0.005, 'gamma': 0.995},
            {'rotation': 0.2, 'noise': 0.5, 'brightness': 0.001}
        ]
        
        image_files = [f for f in os.listdir(f'{source_dir}/images') if f.endswith('.jpg')]
        
        # Optimal split for stability (more validation for reliable metrics)
        random.shuffle(image_files)
        split_idx = int(0.75 * len(image_files))  # 75/25 split for stable validation
        train_images = image_files[:split_idx]
        val_images = image_files[split_idx:]
        
        print(f"📊 Train: {len(train_images)} | Val: {len(val_images)} (stable split)")
        
        total_generated = 0
        
        # Generate stable training data
        for img_file in train_images:
            base_name = img_file.replace('.jpg', '')
            img_path = f'{source_dir}/images/{img_file}'
            label_path = f'{source_dir}/labels/{base_name}.txt'
            
            image = cv2.imread(img_path)
            
            # Original (always include)
            cv2.imwrite(f'{target_dir}/train/images/{img_file}', image)
            if os.path.exists(label_path):
                shutil.copy2(label_path, f'{target_dir}/train/labels/{base_name}.txt')
            total_generated += 1
            
            # Stable augmentations (fewer, higher quality)
            for i, aug_params in enumerate(stable_augmentations):
                aug_image = self.apply_stable_augmentation(image, aug_params)
                
                aug_name = f'{base_name}_stable_{i}.jpg'
                cv2.imwrite(f'{target_dir}/train/images/{aug_name}', aug_image)
                
                if os.path.exists(label_path):
                    shutil.copy2(label_path, f'{target_dir}/train/labels/{base_name}_stable_{i}.txt')
                
                total_generated += 1
        
        # Validation data (pristine, larger set)
        for img_file in val_images:
            base_name = img_file.replace('.jpg', '')
            src_img = f'{source_dir}/images/{img_file}'
            src_label = f'{source_dir}/labels/{base_name}.txt'
            
            shutil.copy2(src_img, f'{target_dir}/val/images/{img_file}')
            if os.path.exists(src_label):
                shutil.copy2(src_label, f'{target_dir}/val/labels/{base_name}.txt')
        
        print(f"✅ Generated {total_generated} ultra-stable samples")
        
        # Dataset config
        config = {
            'path': os.path.abspath(target_dir),
            'train': 'train/images',
            'val': 'val/images',
            'nc': 12,
            'names': [
                'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
                'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
            ]
        }
        
        with open('v4_fizzi_stable_dataset.yaml', 'w') as f:
            yaml.dump(config, f)
        
        return 'v4_fizzi_stable_dataset.yaml', total_generated
    
    def apply_stable_augmentation(self, image, params):
        """Apply ultra-stable augmentation for consistent performance."""
        
        aug_image = image.copy().astype(np.float32)
        
        if 'brightness' in params:
            aug_image += params['brightness'] * 25  # Reduced intensity
        
        if 'contrast' in params:
            aug_image = aug_image * (1 + params['contrast'])
        
        if 'gamma' in params:
            gamma = params['gamma']
            aug_image = np.power(aug_image / 255.0, 1.0 / gamma) * 255.0
        
        if 'rotation' in params:
            h, w = aug_image.shape[:2]
            center = (w // 2, h // 2)
            matrix = cv2.getRotationMatrix2D(center, params['rotation'], 1.0)
            aug_image = cv2.warpAffine(aug_image, matrix, (w, h))
        
        if 'noise' in params:
            noise = np.random.normal(0, params['noise'], aug_image.shape)
            aug_image += noise
        
        return np.clip(aug_image, 0, 255).astype(np.uint8)
    
    def stability_focused_training(self, dataset_yaml):
        """Stability-focused training for sustained 90%+ performance."""
        
        print("\n🎯 STABILITY-FOCUSED TRAINING")
        print("=" * 50)
        
        # Use best V3 model as starting point
        base_model = 'v3_fizzi_rapid_best.pt' if os.path.exists('v3_fizzi_rapid_best.pt') else 'best.pt'
        print(f"🤖 Starting from: {base_model}")
        
        model = YOLO(base_model)
        
        # Ultra-stable training parameters
        stable_args = {
            'data': dataset_yaml,
            'epochs': 200,           # More epochs for stability
            'imgsz': 416,
            'batch': 8,              # Smaller batch for stability
            'lr0': 0.002,            # Conservative learning rate
            'lrf': 0.0001,           # Very low final LR
            'weight_decay': 0.0001,  # Light regularization
            'warmup_epochs': 20,     # Extended warmup
            'patience': 50,          # High patience for stability
            'project': 'v4_fizzi_stable',
            'name': 'sustained_excellence',
            'exist_ok': True,
            'optimizer': 'AdamW',
            'cos_lr': True,
            'close_mosaic': 50,      # Late mosaic closure
            'amp': True,
            'augment': True,
            'multi_scale': False,    # Disable for stability
            'rect': True,            # Enable for efficiency
            'overlap_mask': True,
            'mask_ratio': 4,
            'dropout': 0.0,
            'label_smoothing': 0.0,
            'box': 5.0,              # Balanced loss weights
            'cls': 1.0,
            'dfl': 1.0,
            'hsv_h': 0.005,          # Minimal augmentation
            'hsv_s': 0.2,
            'hsv_v': 0.1,
            'degrees': 1.0,          # Tiny rotation
            'translate': 0.02,       # Minimal translation
            'scale': 0.98,           # Minimal scaling
            'shear': 0.0,            # No shearing
            'perspective': 0.0,      # No perspective
            'flipud': 0.0,           # No vertical flip
            'fliplr': 0.3,           # Light horizontal flip
            'mosaic': 0.5,           # Reduced mosaic
            'mixup': 0.0,            # No mixup for stability
            'copy_paste': 0.0,       # No copy-paste for stability
            'verbose': True,
            'save_period': 10,
            'plots': True,
            'val': True,
            'freeze': None
        }
        
        print("📊 Stability Parameters:")
        key_params = ['epochs', 'batch', 'lr0', 'patience', 'mosaic', 'mixup']
        for key in key_params:
            print(f"  {key}: {stable_args[key]}")
        
        try:
            print("🎯 Starting stability-focused training...")
            results = model.train(**stable_args)
            
            model_path = 'v4_fizzi_stable/sustained_excellence/weights/best.pt'
            return model_path, results
            
        except Exception as e:
            print(f"❌ Stability training failed: {e}")
            return None, None

    def consistency_enforced_training(self, dataset_yaml):
        """Consistency-enforced training with target maintenance."""

        print("\n💪 CONSISTENCY-ENFORCED TRAINING")
        print("=" * 50)

        # Start from stability model if available
        base_model = 'v4_fizzi_stable/sustained_excellence/weights/best.pt'
        if not os.path.exists(base_model):
            base_model = 'v3_fizzi_rapid_best.pt' if os.path.exists('v3_fizzi_rapid_best.pt') else 'best.pt'

        print(f"🤖 Starting from: {base_model}")
        model = YOLO(base_model)

        # Consistency-enforced parameters
        consistency_args = {
            'data': dataset_yaml,
            'epochs': 150,           # Focused epochs
            'imgsz': 416,
            'batch': 4,              # Very small batch for precision
            'lr0': 0.001,            # Ultra-conservative LR
            'lrf': 0.00001,          # Extremely low final LR
            'weight_decay': 0.00005, # Minimal regularization
            'warmup_epochs': 30,     # Extended warmup
            'patience': 75,          # Maximum patience
            'project': 'v4_fizzi_stable',
            'name': 'consistency_enforced',
            'exist_ok': True,
            'optimizer': 'AdamW',
            'cos_lr': True,
            'close_mosaic': 100,     # Very late mosaic closure
            'amp': True,
            'augment': False,        # No augmentation for consistency
            'multi_scale': False,
            'rect': True,
            'overlap_mask': True,
            'mask_ratio': 4,
            'dropout': 0.0,
            'label_smoothing': 0.0,
            'box': 3.0,              # Lower loss weights for stability
            'cls': 0.5,
            'dfl': 0.5,
            'hsv_h': 0.0,            # No HSV augmentation
            'hsv_s': 0.0,
            'hsv_v': 0.0,
            'degrees': 0.0,          # No rotation
            'translate': 0.0,        # No translation
            'scale': 1.0,            # No scaling
            'shear': 0.0,
            'perspective': 0.0,
            'flipud': 0.0,
            'fliplr': 0.0,           # No flipping for consistency
            'mosaic': 0.0,           # No mosaic for consistency
            'mixup': 0.0,
            'copy_paste': 0.0,
            'verbose': True,
            'save_period': 5,
            'plots': True,
            'val': True,
            'freeze': None
        }

        print("📊 Consistency Parameters:")
        key_params = ['epochs', 'batch', 'lr0', 'augment', 'mosaic']
        for key in key_params:
            print(f"  {key}: {consistency_args[key]}")

        try:
            print("💪 Starting consistency-enforced training...")
            results = model.train(**consistency_args)

            model_path = 'v4_fizzi_stable/consistency_enforced/weights/best.pt'
            return model_path, results

        except Exception as e:
            print(f"❌ Consistency training failed: {e}")
            return None, None

    def evaluate_with_consistency_tracking(self, model_path, dataset_yaml):
        """Evaluate model with consistency tracking."""

        if not os.path.exists(model_path):
            return None, False

        try:
            model = YOLO(model_path)

            # Multiple evaluations for consistency
            evaluations = []
            for i in range(5):  # 5 evaluations for consistency check
                results = model.val(
                    data=dataset_yaml,
                    imgsz=416,
                    batch=1,
                    verbose=False,
                    plots=False
                )

                metrics = {
                    'precision': getattr(results.box, 'mp', 0),
                    'recall': getattr(results.box, 'mr', 0),
                    'map50': getattr(results.box, 'map50', 0),
                    'map50_95': getattr(results.box, 'map', 0)
                }
                evaluations.append(metrics)

            # Calculate average and consistency
            avg_metrics = {}
            consistency_scores = {}

            for metric_name in self.targets.keys():
                values = [eval_result[metric_name] for eval_result in evaluations]
                avg_metrics[metric_name] = np.mean(values)
                consistency_scores[metric_name] = 1.0 - (np.std(values) / np.mean(values)) if np.mean(values) > 0 else 0

            # Check if consistently meets targets
            consistent_achievement = all(
                avg_metrics[metric] >= target and consistency_scores[metric] >= 0.95
                for metric, target in self.targets.items()
            )

            return avg_metrics, consistent_achievement, consistency_scores

        except Exception as e:
            print(f"❌ Error evaluating {model_path}: {e}")
            return None, False, None

    def check_sustained_excellence(self, metrics, consistency_scores):
        """Check if sustained excellence is achieved."""

        if not metrics or not consistency_scores:
            return False, {}

        achievements = {}
        all_sustained = True

        for metric_name, target in self.targets.items():
            current = metrics.get(metric_name, 0)
            consistency = consistency_scores.get(metric_name, 0)

            target_achieved = current >= target
            consistency_achieved = consistency >= 0.95  # 95% consistency required
            sustained = target_achieved and consistency_achieved

            achievements[metric_name] = {
                'current': current,
                'target': target,
                'consistency': consistency,
                'target_achieved': target_achieved,
                'consistency_achieved': consistency_achieved,
                'sustained': sustained,
                'percentage': current * 100
            }

            if not sustained:
                all_sustained = False

        return all_sustained, achievements

    def execute_v4_sustained_excellence(self):
        """Execute V4 Fizzi sustained excellence pipeline."""

        print("🚀 EXECUTING V4 FIZZI SUSTAINED EXCELLENCE")
        print("=" * 60)
        print("🎯 CHALLENGE: ACHIEVE AND MAINTAIN 90%+ ALL METRICS")

        # Step 1: Create ultra-stable dataset
        dataset_yaml, sample_count = self.create_ultra_stable_dataset()
        print(f"✅ Step 1: Created {sample_count} ultra-stable samples")

        # Step 2: Stability-focused training
        print("\n🎯 Step 2: Stability-Focused Training")
        stable_model, stable_results = self.stability_focused_training(dataset_yaml)

        # Step 3: Consistency-enforced training
        print("\n💪 Step 3: Consistency-Enforced Training")
        consistent_model, consistent_results = self.consistency_enforced_training(dataset_yaml)

        # Step 4: Comprehensive evaluation with consistency tracking
        print("\n📊 Step 4: Sustained Excellence Evaluation")

        models_to_evaluate = []
        if stable_model:
            models_to_evaluate.append(('Stability-Focused', stable_model))
        if consistent_model:
            models_to_evaluate.append(('Consistency-Enforced', consistent_model))

        best_model = None
        best_metrics = None
        sustained_excellence_achieved = False

        for model_name, model_path in models_to_evaluate:
            print(f"\n📊 Evaluating {model_name} for sustained excellence...")

            eval_result = self.evaluate_with_consistency_tracking(model_path, dataset_yaml)
            if eval_result[0] is None:
                continue

            metrics, consistent_achievement, consistency_scores = eval_result

            sustained, achievements = self.check_sustained_excellence(metrics, consistency_scores)

            print(f"\n🏆 {model_name} Results:")
            for metric_name, data in achievements.items():
                target_status = "✅" if data['target_achieved'] else "❌"
                consistency_status = "🎯" if data['consistency_achieved'] else "⚠️"
                sustained_status = "🏆" if data['sustained'] else "❌"

                print(f"   {metric_name}: {data['percentage']:.1f}% {target_status} | "
                      f"Consistency: {data['consistency']*100:.1f}% {consistency_status} | "
                      f"Sustained: {sustained_status}")

            if sustained:
                print(f"🎉 SUSTAINED EXCELLENCE ACHIEVED with {model_name}!")
                sustained_excellence_achieved = True
                best_model = model_path
                best_metrics = metrics
                break
            elif not best_metrics or sum(metrics.values()) > sum(best_metrics.values()):
                best_model = model_path
                best_metrics = metrics

        # Final results
        print(f"\n🏆 V4 FIZZI SUSTAINED EXCELLENCE RESULTS")
        print("=" * 60)

        if sustained_excellence_achieved:
            print("🎉 SUSTAINED EXCELLENCE ACHIEVED! 90%+ ALL metrics with 95%+ consistency!")
            final_path = 'v4_fizzi_sustained_excellence_success.pt'
        else:
            print("📈 Progress toward sustained excellence - continue optimization")
            final_path = 'v4_fizzi_sustained_excellence_best.pt'

        if best_model:
            shutil.copy2(best_model, final_path)
            print(f"💾 Final model saved: {final_path}")

        return final_path, best_metrics, sustained_excellence_achieved

def main():
    """Execute V4 Fizzi sustained excellence."""

    excellence = V4FizziSustainedExcellence()

    try:
        final_model, final_metrics, success = excellence.execute_v4_sustained_excellence()

        print(f"\n🚀 V4 FIZZI SUSTAINED EXCELLENCE COMPLETED")
        print(f"🏆 Final Model: {final_model}")

        if success:
            print(f"🎉 SUSTAINED EXCELLENCE ACHIEVED! 90%+ ALL metrics with consistency! 🎉")
        else:
            print(f"📈 Significant progress toward sustained excellence")

    except Exception as e:
        print(f"❌ V4 sustained excellence failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
