"""
Train a YOLO model on the targeted augmented dataset for chess piece detection.
This script uses the exact configuration from the first successful v11n model.
"""

import os
import sys
import torch
import argparse
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from ultralytics import YOLO
from pathlib import Path
from sklearn.metrics import confusion_matrix
import seaborn as sns

def print_system_info():
    """Print system information for debugging and record keeping."""
    print(f"Python version: {sys.version}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")

    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name(0)}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.2f} GB")
        print(f"CUDA Version: {torch.version.cuda}")
        if hasattr(torch, 'backends') and hasattr(torch.backends, 'cudnn'):
            print(f"cuDNN Version: {torch.backends.cudnn.version()}")

    if torch.cuda.is_available():
        device = torch.cuda.current_device()
        print(f"Training on: {device}")
        print(f"GPU: {torch.cuda.get_device_name(device)}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(device).total_memory / 1e9:.2f} GB")
    print()

def visualize_problematic_pieces(model, dataset_path, output_dir, run_name):
    """
    Visualize predictions for problematic pieces to analyze detection quality.

    Args:
        model: Trained YOLO model
        dataset_path: Path to the dataset YAML file
        output_dir: Directory to save visualizations
        run_name: Name of the current run
    """
    try:
        print("\nVisualizing predictions for problematic pieces...")

        # Load dataset configuration
        import yaml
        with open(dataset_path, 'r') as f:
            dataset_config = yaml.safe_load(f)

        # Get validation images directory
        val_images_dir = os.path.join(os.path.dirname(dataset_path), dataset_config.get('val', 'images/val'))

        # Create visualization directory
        vis_dir = os.path.join(output_dir, run_name, 'visualizations')
        os.makedirs(vis_dir, exist_ok=True)

        # Problematic piece class IDs
        problematic_pieces = [1, 2, 4, 8, 10]  # white_knight, white_bishop, white_queen, black_bishop, black_queen
        class_names = ["white_pawn", "white_knight", "white_bishop", "white_rook", "white_queen", "white_king",
                      "black_pawn", "black_knight", "black_bishop", "black_rook", "black_queen", "black_king"]

        # Get a sample of validation images
        import glob
        val_images = glob.glob(os.path.join(val_images_dir, '*.jpg'))

        if not val_images:
            print(f"No validation images found in {val_images_dir}")
            return

        # Limit to 10 images for visualization
        val_images = val_images[:min(10, len(val_images))]

        print(f"Visualizing predictions on {len(val_images)} validation images...")

        # Run predictions and save visualizations
        for img_path in val_images:
            results = model.predict(img_path, conf=0.25, save=True, save_txt=True, save_conf=True)

            # Check if any problematic pieces were detected
            detected_problematic = False
            for result in results:
                if result.boxes is not None and len(result.boxes) > 0:
                    cls = result.boxes.cls.cpu().numpy()
                    if any(c in problematic_pieces for c in cls):
                        detected_problematic = True
                        break

            if detected_problematic:
                # Copy the prediction image to our visualization directory
                img_name = os.path.basename(img_path)
                pred_img_path = os.path.join(os.path.dirname(results[0].save_dir), img_name)
                if os.path.exists(pred_img_path):
                    import shutil
                    shutil.copy(pred_img_path, os.path.join(vis_dir, f"pred_{img_name}"))

        print(f"Visualizations saved to {vis_dir}")

    except Exception as e:
        print(f"Error visualizing problematic pieces: {e}")

def find_optimal_lr(model, dataset_path, batch_size=16, img_size=416, device=0):
    """
    Find the optimal learning rate using the learning rate finder.

    Args:
        model: YOLO model
        dataset_path: Path to the dataset YAML file
        batch_size: Batch size for training
        img_size: Image size for training
        device: Device to train on

    Returns:
        Optimal learning rate
    """
    try:
        print("\nRunning learning rate finder...")

        # Create a temporary directory for LR finder results
        import tempfile
        temp_dir = tempfile.mkdtemp()

        # Run the learning rate finder
        results = model.tune(
            data=dataset_path,
            imgsz=img_size,
            batch=batch_size,
            device=device,
            plots=True,
            save_dir=temp_dir,
            verbose=True
        )

        # Get the suggested learning rate
        if hasattr(results, 'lr') and results.lr is not None:
            suggested_lr = results.lr
            print(f"Learning rate finder suggests: {suggested_lr:.6f}")

            # Plot saved to results.save_dir
            print(f"Learning rate plot saved to {results.save_dir}")

            return suggested_lr
        else:
            print("Learning rate finder did not return a suggested learning rate")
            return 0.01  # Default to 0.01

    except Exception as e:
        print(f"Error finding optimal learning rate: {e}")
        return 0.01  # Default to 0.01

def train_targeted_model(
    dataset_path,
    model_name="yolo11n.pt",
    epochs=100,
    patience=100,
    output_dir=None,
    find_lr=False
):
    """
    Train a YOLO model on the targeted augmented dataset using the configuration
    from the first successful v11n model.

    Args:
        dataset_path: Path to the dataset YAML file
        model_name: Name of the model to use (default: yolo11n.pt)
        epochs: Maximum number of epochs to train
        patience: Number of epochs to wait for improvement before early stopping
        output_dir: Directory to save the output model
    """
    # Create timestamp for unique run name
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    run_name = f"targeted_{timestamp}"

    # Set up project directory
    if output_dir is None:
        output_dir = os.path.join("chess_board_detection", "piece_detection", "models", "targeted_yolo")
    os.makedirs(output_dir, exist_ok=True)

    # Print system information
    print_system_info()

    print(f"=== Training Targeted Model (max {epochs} epochs) ===")
    print(f"Dataset: {dataset_path}")
    print(f"Model: {model_name}")
    print(f"Project directory: {output_dir}")

    # Load the model
    model = YOLO(model_name)

    # Find optimal learning rate if requested
    lr0_value = 0.01  # Default from successful model
    if find_lr:
        print("Finding optimal learning rate...")
        lr0_value = find_optimal_lr(model, dataset_path, batch_size=16, img_size=416, device=0)
        print(f"Using learning rate: {lr0_value}")

    # Note: Class weights removed as cls_weights parameter is not supported in current Ultralytics version
    # Will rely on aggressive augmentation and loss weights for problematic piece focus
    print("Using aggressive augmentation and optimized loss weights for problematic piece focus")

    # Configure training parameters using the exact configuration from the first successful v11n model
    results = model.train(
        # Basic Configuration
        data=dataset_path,
        epochs=epochs,
        imgsz=416,
        batch=16,
        device=0,
        project=output_dir,
        name=run_name,
        exist_ok=True,
        pretrained=True,
        verbose=True,
        seed=42,
        patience=patience,

        # Optimization Settings
        lr0=lr0_value,
        lrf=lr0_value,
        momentum=0.937,
        weight_decay=0.0005,
        warmup_epochs=3.0,
        warmup_momentum=0.8,
        warmup_bias_lr=0.1,

        # Augmentation Settings
        augment=True,
        mosaic=1.0,
        mixup=0.5,
        degrees=15.0,
        translate=0.2,
        scale=0.5,
        shear=2.0,
        fliplr=0.5,
        perspective=0.0005,
        hsv_h=0.015,
        hsv_s=0.7,
        hsv_v=0.4,
        close_mosaic=50,

        # Loss Function Weights
        box=7.5,
        cls=0.5,
        dfl=1.5,

        # Advanced Features
        amp=True,
        cache=True,
        iou=0.7,
        max_det=300,

        # Validation and Saving
        val=True,
        save=True,
        save_period=10,
    )

    # Print final metrics
    metrics = results.results_dict
    best_map50 = metrics.get('metrics/mAP50(B)', 0)
    best_precision = metrics.get('metrics/precision(B)', 0)
    best_recall = metrics.get('metrics/recall(B)', 0)
    best_epoch = metrics.get('best/epoch', 0)

    print("\n=== Final Training Results ===")
    print(f"Best mAP50: {best_map50:.4f}")
    print(f"Best mAP50-95: {metrics.get('metrics/mAP50-95(B)', 'N/A'):.4f}")
    print(f"Best Precision: {best_precision:.4f}")
    print(f"Best Recall: {best_recall:.4f}")
    print(f"Best achieved at epoch: {best_epoch}")

    # Print per-class metrics if available
    if 'metrics/precision_per_class(B)' in metrics:
        print("\n=== Per-Class Metrics ===")
        class_names = ["white_pawn", "white_knight", "white_bishop", "white_rook", "white_queen", "white_king",
                      "black_pawn", "black_knight", "black_bishop", "black_rook", "black_queen", "black_king"]

        precision_per_class = metrics.get('metrics/precision_per_class(B)', [])
        recall_per_class = metrics.get('metrics/recall_per_class(B)', [])
        map50_per_class = metrics.get('metrics/mAP50_per_class(B)', [])

        print(f"{'Class':<15} {'Precision':<10} {'Recall':<10} {'mAP50':<10}")
        print("-" * 45)

        for i, class_name in enumerate(class_names):
            if i < len(precision_per_class) and i < len(recall_per_class) and i < len(map50_per_class):
                print(f"{class_name:<15} {precision_per_class[i]:.4f}      {recall_per_class[i]:.4f}      {map50_per_class[i]:.4f}")

    # Generate and analyze confusion matrix
    try:
        print("\nGenerating confusion matrix for validation set...")
        class_names = ["white_pawn", "white_knight", "white_bishop", "white_rook", "white_queen", "white_king",
                      "black_pawn", "black_knight", "black_bishop", "black_rook", "black_queen", "black_king"]

        # Run validation with confusion matrix enabled
        val_results = model.val(conf=0.25, iou=0.7, max_det=300, verbose=True)

        # Get confusion matrix data if available
        if hasattr(val_results, 'confusion_matrix') and val_results.confusion_matrix is not None:
            conf_matrix = val_results.confusion_matrix.matrix

            # Plot confusion matrix
            plt.figure(figsize=(12, 10))
            sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues',
                        xticklabels=class_names, yticklabels=class_names)
            plt.xlabel('Predicted')
            plt.ylabel('True')
            plt.title('Confusion Matrix')

            # Save confusion matrix
            confusion_matrix_path = os.path.join(output_dir, run_name, 'confusion_matrix.png')
            plt.savefig(confusion_matrix_path)
            print(f"Confusion matrix saved to {confusion_matrix_path}")

            # Analyze problematic pieces
            problematic_pieces = [1, 2, 4, 8, 10]  # white_knight, white_bishop, white_queen, black_bishop, black_queen

            print("\n=== Confusion Analysis for Problematic Pieces ===")
            for idx in problematic_pieces:
                piece_name = class_names[idx]
                true_positives = conf_matrix[idx, idx]
                false_positives = conf_matrix[:, idx].sum() - true_positives
                false_negatives = conf_matrix[idx, :].sum() - true_positives

                # Find top confusions (what this piece is confused with)
                confusions = []
                for i in range(len(class_names)):
                    if i != idx and conf_matrix[idx, i] > 0:
                        confusions.append((class_names[i], conf_matrix[idx, i]))

                confusions.sort(key=lambda x: x[1], reverse=True)

                print(f"\n{piece_name}:")
                print(f"  True Positives: {true_positives}")
                print(f"  False Positives: {false_positives}")
                print(f"  False Negatives: {false_negatives}")

                if confusions:
                    print(f"  Top confusions (mistaken as):")
                    for confused_class, count in confusions[:3]:
                        print(f"    - {confused_class}: {count}")
                else:
                    print("  No confusions found")
        else:
            print("Confusion matrix not available")

    except Exception as e:
        print(f"Error generating confusion matrix: {e}")

    # Visualize predictions for problematic pieces
    visualize_problematic_pieces(model, dataset_path, output_dir, run_name)

    # Export the model to ONNX format
    try:
        print("\nExporting model to ONNX format...")
        model.export(format='onnx', dynamic=True, simplify=True)
        print(f"Model exported successfully")
    except Exception as e:
        print(f"Error exporting model: {e}")

    return results

def main():
    parser = argparse.ArgumentParser(description="Train a YOLO model on the targeted augmented dataset")
    parser.add_argument("--dataset", type=str, default="chess_board_detection/piece_detection/targeted_dataset/dataset.yaml",
                        help="Path to dataset YAML file")
    parser.add_argument("--model", type=str, default="yolo11n.pt",
                        help="Path to model file")
    parser.add_argument("--epochs", type=int, default=100,
                        help="Maximum number of epochs to train")
    parser.add_argument("--patience", type=int, default=100,
                        help="Number of epochs to wait for improvement before early stopping")
    parser.add_argument("--output_dir", type=str, default=None,
                        help="Directory to save the output model")
    parser.add_argument("--find_lr", action="store_true",
                        help="Run learning rate finder before training")

    args = parser.parse_args()

    train_targeted_model(
        dataset_path=args.dataset,
        model_name=args.model,
        epochs=args.epochs,
        patience=args.patience,
        output_dir=args.output_dir,
        find_lr=args.find_lr
    )

if __name__ == "__main__":
    main()
