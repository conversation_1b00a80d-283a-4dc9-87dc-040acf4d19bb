PS C:\Users\<USER>\OneDrive\Desktop\fizzi chess> python open_weights_breakthrough_v2.py
🚀 OPEN WEIGHTS BREAKTHROUGH V2
==================================================
🎯 Target: 99% mAP50 on fizzi piece set
⚡ Strategy: Full model adaptation (no weight preservation)
🤖 Base model: best.pt (5.20 MB)
🔓 All weights open for optimization
🚀 EXECUTING OPEN WEIGHTS BREAKTHROUGH
============================================================
🔄 Creating enhanced dataset with heavy augmentation...
✅ Generated 2544 training samples
✅ Step 1: Created 2544 training samples

🚀 Step 2: Aggressive Open Weights Training
🚀 AGGRESSIVE OPEN WEIGHTS TRAINING
📊 Aggressive Training Parameters:
  epochs: 200
  batch: 8
  lr0: 0.01
  freeze: None
  mixup: 0.2
  copy_paste: 0.3
New https://pypi.org/project/ultralytics/8.3.148 available  Update with 'pip install -U ultralytics'
WARNING 'label_smoothing' is deprecated and will be removed in in the future.
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)
engine\trainer: agnostic_nms=False, amp=True, augment=True, auto_augment=randaugment, batch=8, bgr=0.0, box=7.5, cache=False, cfg=None, classes=None, close_mosaic=30, cls=0.5, conf=None, copy_paste=0.3, copy_paste_mode=flip, cos_lr=True, cutmix=0.0, data=fizzi_enhanced_dataset.yaml, degrees=10.0, deterministic=True, device=None, dfl=1.5, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=200, erasing=0.4, exist_ok=True, fliplr=0.5, flipud=0.0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0.02, hsv_s=0.8, hsv_v=0.5, imgsz=416, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.01, lrf=0.0001, mask_ratio=4, max_det=300, mixup=0.2, mode=train, model=best.pt, momentum=0.937, mosaic=1.0, multi_scale=True, name=aggressive_open, nbs=64, nms=False, opset=None, optimize=False, optimizer=AdamW, overlap_mask=True, patience=50, perspective=0.0, plots=False, pose=12.0, pretrained=True, profile=False, project=open_weights_breakthrough, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=open_weights_breakthrough\aggressive_open, save_frames=False, save_json=False, save_period=20, save_txt=False, scale=0.9, seed=0, shear=2.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0.1, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=10, warmup_momentum=0.8, weight_decay=0.0005, workers=8, workspace=None

                   from  n    params  module                                       arguments
  0                  -1  1       464  ultralytics.nn.modules.conv.Conv             [3, 16, 3, 2]
  1                  -1  1      4672  ultralytics.nn.modules.conv.Conv             [16, 32, 3, 2]
  2                  -1  1      6640  ultralytics.nn.modules.block.C3k2            [32, 64, 1, False, 0.25]        
  3                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]
  4                  -1  1     26080  ultralytics.nn.modules.block.C3k2            [64, 128, 1, False, 0.25]       
  5                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]
  6                  -1  1     87040  ultralytics.nn.modules.block.C3k2            [128, 128, 1, True]
  7                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]
  8                  -1  1    346112  ultralytics.nn.modules.block.C3k2            [256, 256, 1, True]
  9                  -1  1    164608  ultralytics.nn.modules.block.SPPF            [256, 256, 5]
 10                  -1  1    249728  ultralytics.nn.modules.block.C2PSA           [256, 256, 1]
 11                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 12             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 13                  -1  1    111296  ultralytics.nn.modules.block.C3k2            [384, 128, 1, False]
 14                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 15             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 16                  -1  1     32096  ultralytics.nn.modules.block.C3k2            [256, 64, 1, False]
 17                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]
 18            [-1, 13]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 19                  -1  1     86720  ultralytics.nn.modules.block.C3k2            [192, 128, 1, False]
 20                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]
 21            [-1, 10]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 22                  -1  1    378880  ultralytics.nn.modules.block.C3k2            [384, 256, 1, True]
 23        [16, 19, 22]  1    433012  ultralytics.nn.modules.head.Detect           [12, [64, 128, 256]]
YOLO11n summary: 181 layers, 2,592,180 parameters, 2,592,164 gradients, 6.5 GFLOPs

Transferred 499/499 items from pretrained weights
Freezing layer 'model.23.dfl.conv.weight'
AMP: running Automatic Mixed Precision (AMP) checks...
AMP: checks passed 
train: Fast image access  (ping: 0.10.0 ms, read: 145.1310.1 MB/s, size: 82.1 KB)
train: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\fizzi_enhanced_dataset\labels... 2544 images, 
train: New cache created: C:\Users\<USER>\OneDrive\Desktop\fizzi chess\fizzi_enhanced_dataset\labels.cache
albumentations: Blur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))
val: Fast image access  (ping: 0.10.0 ms, read: 654.7243.2 MB/s, size: 78.4 KB)
val: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\fizzi_416x416_dataset\labels.cache... 48 images,
optimizer: AdamW(lr=0.01, momentum=0.937) with parameter groups 81 weight(decay=0.0), 88 weight(decay=0.0005), 87 bias(decay=0.0)
Image sizes 416 train, 416 val
Using 8 dataloader workers
Logging results to open_weights_breakthrough\aggressive_open
Starting training for 200 epochs...

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      1/200      1.45G      2.309      4.399      1.176          5        416: 100%|██████████| 318/318 [00:40<00:
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 3/3 [00 
                   all         48         48      0.481      0.368      0.231      0.142

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      2/200      1.45G      2.369      3.662       1.18         23        320: 100%|██████████| 318/318 [00:33<00:
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 3/3 [00
                   all         48         48      0.844      0.104      0.119     0.0664

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      3/200      1.45G      2.344      3.533      1.158          8        384: 100%|██████████| 318/318 [00:32<00:
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 3/3 [00 
                   all         48         48      0.551      0.188      0.119     0.0726
