PS C:\Users\<USER>\OneDrive\Desktop\fizzi chess> python v3_fizzi_rapid_breakthrough.py
🚀 V3 FIZZI - RAPID BREAKTHROUGH (<100 EPOCHS)
============================================================
🎯 TARGETS:
   📊 Precision: 90.0%+
   📊 Recall: 90.0%+
   📊 mAP50: 90.0%+
   📊 mAP50-95: 90.0%+
⚡ RAPID OPTIMIZATION STRATEGY
🚀 EXECUTING V3 FIZZI RAPID BREAKTHROUGH
============================================================
🎯 TARGET: 90%+ ALL metrics in <100 epochs

🎯 CREATING PERFECT RAPID-LEARNING DATASET
==================================================
📊 Train: 40 | Val: 8
C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v3_fizzi_rapid_breakthrough.py:149: RuntimeWarning: invalid value encountered in power
  aug_image = np.power(aug_image / 255.0, 1.0 / gamma) * 255.0
C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v3_fizzi_rapid_breakthrough.py:173: RuntimeWarning: invalid value encountered in cast
  return np.clip(aug_image, 0, 255).astype(np.uint8)
✅ Generated 640 perfect training samples
✅ Step 1: Created 640 perfect samples

🚀 Step 2: Rapid Breakthrough Training (80 epochs)

🚀 RAPID BREAKTHROUGH TRAINING
==================================================
🤖 Starting from: best.pt
📊 Rapid Training Parameters:
  epochs: 80
  batch: 16
  lr0: 0.01
  mixup: 0.1
  copy_paste: 0.1
🚀 Starting rapid breakthrough training...
New https://pypi.org/project/ultralytics/8.3.148 available  Update with 'pip install -U ultralytics'
WARNING 'label_smoothing' is deprecated and will be removed in in the future.
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)      
engine\trainer: agnostic_nms=False, amp=True, augment=True, auto_augment=randaugment, batch=16, bgr=0.0, box=7.5, cache=False, cfg=None, classes=None, close_mosaic=20, cls=0.5, conf=None, copy_paste=0.1, copy_paste_mode=flip, cos_lr=True, cutmix=0.0, data=v3_fizzi_rapid_dataset.yaml, degrees=5.0, deterministic=True, device=None, dfl=1.5, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=80, erasing=0.4, exist_ok=True, fliplr=0.5, flipud=0.0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, imgsz=416, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.01, lrf=0.001, mask_ratio=4, max_det=300, mixup=0.1, mode=train, model=best.pt, momentum=0.937, mosaic=1.0, multi_scale=True, name=breakthrough_80, nbs=64, nms=False, opset=None, optimize=False, optimizer=AdamW, overlap_mask=True, patience=25, perspective=0.0, plots=False, pose=12.0, pretrained=True, profile=False, project=v3_fizzi_rapid, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=v3_fizzi_rapid\breakthrough_80, save_frames=False, save_json=False, save_period=10, save_txt=False, scale=0.9, seed=0, shear=1.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0.1, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=5, warmup_momentum=0.8, weight_decay=0.0005, workers=8, workspace=None

                   from  n    params  module                                       arguments
  0                  -1  1       464  ultralytics.nn.modules.conv.Conv             [3, 16, 3, 2]
  1                  -1  1      4672  ultralytics.nn.modules.conv.Conv             [16, 32, 3, 2]
  2                  -1  1      6640  ultralytics.nn.modules.block.C3k2            [32, 64, 1, False, 0.25]        
  3                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]
  4                  -1  1     26080  ultralytics.nn.modules.block.C3k2            [64, 128, 1, False, 0.25]       
  5                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]
  6                  -1  1     87040  ultralytics.nn.modules.block.C3k2            [128, 128, 1, True]
  7                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]
  8                  -1  1    346112  ultralytics.nn.modules.block.C3k2            [256, 256, 1, True]
  9                  -1  1    164608  ultralytics.nn.modules.block.SPPF            [256, 256, 5]
 10                  -1  1    249728  ultralytics.nn.modules.block.C2PSA           [256, 256, 1]
 11                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 12             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 13                  -1  1    111296  ultralytics.nn.modules.block.C3k2            [384, 128, 1, False]
 14                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 15             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 16                  -1  1     32096  ultralytics.nn.modules.block.C3k2            [256, 64, 1, False]
 17                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]
 18            [-1, 13]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 19                  -1  1     86720  ultralytics.nn.modules.block.C3k2            [192, 128, 1, False]
 20                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]
 21            [-1, 10]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 22                  -1  1    378880  ultralytics.nn.modules.block.C3k2            [384, 256, 1, True]
 23        [16, 19, 22]  1    433012  ultralytics.nn.modules.head.Detect           [12, [64, 128, 256]]
YOLO11n summary: 181 layers, 2,592,180 parameters, 2,592,164 gradients, 6.5 GFLOPs

Transferred 499/499 items from pretrained weights
Freezing layer 'model.23.dfl.conv.weight'
AMP: running Automatic Mixed Precision (AMP) checks...
AMP: checks passed 
train: Fast image access  (ping: 0.00.0 ms, read: 978.2285.1 MB/s, size: 81.7 KB)
train: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v3_fizzi_rapid_dataset\train\labels... 640 ima 
train: New cache created: C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v3_fizzi_rapid_dataset\train\labels.cache
albumentations: Blur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))
val: Fast image access  (ping: 0.10.0 ms, read: 593.9118.6 MB/s, size: 89.0 KB)
val: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v3_fizzi_rapid_dataset\val\labels... 8 images, 0
val: New cache created: C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v3_fizzi_rapid_dataset\val\labels.cache
optimizer: AdamW(lr=0.01, momentum=0.937) with parameter groups 81 weight(decay=0.0), 88 weight(decay=0.0005), 87 bias(decay=0.0)
Image sizes 416 train, 416 val
Using 8 dataloader workers
Logging results to v3_fizzi_rapid\breakthrough_80
Starting training for 80 epochs...

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       1/80      2.74G      1.737        7.7     0.9626         25        608: 100%|██████████| 40/40 [00:11<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8    0.00439      0.286     0.0171    0.00726

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       2/80      2.75G      1.849      3.412       1.02         28        480: 100%|██████████| 40/40 [00:06<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8          0          0          0          0

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       3/80      2.75G      1.946      3.386       1.02         33        224: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8          0          0          0          0

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       4/80      2.75G      1.799      3.337      1.045         26        576: 100%|██████████| 40/40 [00:06<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8    0.00708      0.429     0.0547     0.0272

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       5/80      2.75G      1.923      3.357      1.004         22        512: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.011      0.143     0.0355     0.0213

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       6/80      2.75G      1.876      3.344     0.9749         29        608: 100%|██████████| 40/40 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8     0.0107      0.429     0.0287     0.0169

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       7/80      2.75G      1.675      3.142     0.9759         23        576: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.774      0.286      0.111     0.0673

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       8/80      2.75G      1.718      2.993       0.98         34        544: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.818      0.143      0.243       0.16

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       9/80      2.75G      1.757      3.119     0.9995         26        480: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.509      0.286      0.264      0.146

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      10/80      2.75G      1.744      3.028     0.9586         23        416: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8    0.00147      0.143     0.0237     0.0144
 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      11/80      2.75G      1.756      3.012      1.013         16        416: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.587      0.143      0.227       0.17

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      12/80      2.75G       1.66      2.748     0.9438         26        288: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.924      0.143      0.321      0.172

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      13/80      2.76G      1.603      2.759     0.9705         28        224: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.206      0.786      0.368      0.217

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      14/80      2.76G      1.632      2.846     0.9541         29        416: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8     0.0115      0.714      0.185      0.138

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      15/80      2.76G      1.509      2.728     0.9376         31        544: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.645      0.286      0.304      0.201

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      16/80      2.76G      1.547      2.662     0.9299         32        192: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8     0.0288      0.857      0.279      0.164

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      17/80      2.76G      1.425      2.552     0.9493         30        512: 100%|██████████| 40/40 [00:06<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.922      0.143      0.406      0.271

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      18/80      2.76G      1.552      2.714     0.9333         23        256: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.227      0.429      0.304      0.142

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      19/80      2.76G      1.545      2.724     0.9328         29        256: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.491      0.286      0.202      0.133

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      20/80      2.76G       1.46      2.556     0.9585         21        224: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8     0.0116      0.714      0.212      0.163

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      21/80      2.76G      1.509      2.631     0.9272         38        416: 100%|██████████| 40/40 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.611      0.143      0.282      0.186

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      22/80      2.76G       1.57      2.548     0.9154         31        512: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.616      0.143      0.182      0.102

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      23/80      2.76G      1.547      2.441     0.9103         26        288: 100%|██████████| 40/40 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.439      0.571      0.369      0.256

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      24/80      2.76G      1.494      2.378     0.9125         30        416: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.663      0.277      0.233       0.14

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      25/80      2.76G      1.496      2.484     0.9254         23        544: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.264      0.429      0.274      0.173
 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      26/80      2.76G      1.427      2.362     0.8946         28        416: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.372      0.271      0.194      0.146

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      27/80      2.76G      1.423      2.358       0.92         29        480: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.225      0.286       0.16      0.107

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      28/80      2.76G      1.387       2.33       0.93         27        256: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.199      0.571      0.259      0.172

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      29/80      2.76G      1.483      2.432     0.9009         29        416: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8     0.0746      0.571      0.208      0.135

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      30/80      2.76G      1.357      2.356     0.9103         31        512: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.387      0.357      0.275      0.214

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      31/80      2.76G      1.352      2.323     0.9227         26        352: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8     0.0241      0.786      0.149     0.0929

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      31/80      2.76G      1.352      2.323     0.9227         26        352: 100%|██████████| 40/40 [00:05<00:00 
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8     0.0241      0.786      0.149     0.0929

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      31/80      2.76G      1.352      2.323     0.9227         26        352: 100%|██████████| 40/40 [00:05<00:00 
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8     0.0241      0.786      0.149     0.0929

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8     0.0241      0.786      0.149     0.0929

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
                   all          8          8     0.0241      0.786      0.149     0.0929

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      32/80      2.76G      1.488      2.403     0.8892         36        224: 100%|██████████| 40/40 [00:04<00:00 
      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      32/80      2.76G      1.488      2.403     0.8892         36        224: 100%|██████████| 40/40 [00:04<00:00 
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
      32/80      2.76G      1.488      2.403     0.8892         36        224: 100%|██████████| 40/40 [00:04<00:00 
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.408      0.571      0.244      0.169

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      33/80      2.76G      1.425      2.361     0.8984         26        224: 100%|██████████| 40/40 [00:05<00:00 
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8     0.0563      0.929       0.29       0.21

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      34/80      2.76G      1.303      2.196      0.894         24        448: 100%|██████████| 40/40 [00:05<00:00 
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.333      0.437      0.465       0.37

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      35/80      2.76G      1.331      2.184     0.8968         28        544: 100%|██████████| 40/40 [00:05<00:00 
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8     0.0668      0.286      0.278      0.198

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      36/80      2.76G      1.271      2.105     0.8925         26        608: 100%|██████████| 40/40 [00:05<00:00 
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.224      0.429      0.445      0.309

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      37/80      2.76G      1.277      2.165     0.9005         21        480: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.399      0.429      0.315      0.193

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      38/80      2.76G      1.343      2.243     0.9077         28        224: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8        0.2      0.288      0.295      0.184

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      39/80      2.76G      1.273       2.12     0.8786         21        480: 100%|██████████| 40/40 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.481        0.4      0.463       0.31                   
  Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      40/80      2.76G      1.206      2.123     0.8904         20        480: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.147      0.714      0.245      0.174

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      41/80      2.76G      1.276      2.182     0.8836         34        320: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.227      0.429      0.255      0.171

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      42/80      2.76G      1.255      2.085     0.8865         30        576: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.532      0.429      0.344      0.214

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      43/80      2.76G      1.211      2.081     0.8923         19        384: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.227      0.429      0.345      0.239

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      44/80      2.76G      1.218      2.111     0.8688         31        480: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.142      0.656      0.324      0.248

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      45/80      2.76G      1.167      2.022      0.889         27        480: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.442      0.434      0.397      0.271

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      46/80      2.76G      1.136      2.013     0.8619         27        544: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.277      0.286      0.316      0.221

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      47/80      2.76G      1.141      1.945     0.8662         29        608: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.308      0.714      0.354      0.241

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      48/80      2.76G      1.168      2.047     0.8619         27        256: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.253      0.424      0.349      0.246

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      49/80      2.76G      1.164      1.999     0.8813         33        288: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.261      0.418      0.311      0.223

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      50/80      2.76G      1.117      2.004     0.8766         20        256: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.105      0.546      0.342      0.227

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      51/80      2.76G      1.129      1.994     0.8721         27        416: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.174      0.786      0.483      0.307

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      52/80      2.76G       1.17      2.003     0.8637         20        256: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.372      0.429       0.32      0.239

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      53/80      2.76G      1.191      2.016     0.8584         24        448: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.093      0.586      0.292      0.187

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      54/80      2.76G      1.187      1.936     0.8606         28        416: 100%|██████████| 40/40 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.079       0.38      0.226      0.158

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      55/80      2.76G      1.131      1.907      0.855         24        480: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8       0.29      0.592      0.353      0.262

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      56/80      2.76G      1.078      1.895     0.8567         34        576: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8        0.3      0.714      0.443      0.332
 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      57/80      2.76G      1.093      1.899     0.8549         24        448: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.245      0.429      0.323      0.221

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      58/80      2.76G      1.126      1.943     0.8552         26        192: 100%|██████████| 40/40 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.114      0.571      0.305      0.216

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      59/80      2.76G      1.068      1.889     0.8652         27        576: 100%|██████████| 40/40 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.136      0.623      0.327      0.235
EarlyStopping: Training stopped early as no improvement observed in last 25 epochs. Best results observed at epoch 34, best model saved as best.pt.
To update EarlyStopping(patience=25) pass a new patience value, i.e. `patience=300` or use `patience=0` to disable EarlyStopping.

59 epochs completed in 0.097 hours.
Optimizer stripped from v3_fizzi_rapid\breakthrough_80\weights\last.pt, 5.4MB
Optimizer stripped from v3_fizzi_rapid\breakthrough_80\weights\best.pt, 5.4MB

Validating v3_fizzi_rapid\breakthrough_80\weights\best.pt...
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)      
YOLO11n summary (fused): 100 layers, 2,584,492 parameters, 0 gradients, 6.3 GFLOPs
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.245      0.448      0.249      0.179
            white_pawn          1          1     0.0232      0.139      0.166     0.0829
            white_rook          1          1          1          0      0.249      0.174
           white_queen          1          1      0.231          1      0.249      0.249
            white_king          1          1      0.232          1      0.497      0.398
            black_pawn          2          2          0          0     0.0865     0.0519
          black_knight          1          1      0.229          1      0.332      0.166
          black_bishop          1          1          0          0      0.166      0.133
Speed: 0.2ms preprocess, 21.4ms inference, 0.0ms loss, 1.0ms postprocess per image

⚡ Step 3: Ultra-Rapid Training (60 epochs)

⚡ ULTRA-RAPID TRAINING
==================================================
📊 Ultra-Rapid Parameters:
  epochs: 60
  batch: 32
  lr0: 0.02
  box: 10.0
  cls: 1.0
  mixup: 0.3
⚡ Starting ultra-rapid training...
New https://pypi.org/project/ultralytics/8.3.148 available  Update with 'pip install -U ultralytics'
WARNING 'label_smoothing' is deprecated and will be removed in in the future.
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)      
engine\trainer: agnostic_nms=False, amp=True, augment=True, auto_augment=randaugment, batch=32, bgr=0.0, box=10.0, cache=False, cfg=None, classes=None, close_mosaic=15, cls=1.0, conf=None, copy_paste=0.3, copy_paste_mode=flip, cos_lr=True, cutmix=0.0, data=v3_fizzi_rapid_dataset.yaml, degrees=8.0, deterministic=True, device=None, dfl=2.0, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=60, erasing=0.4, exist_ok=True, fliplr=0.5, flipud=0.0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0.02, hsv_s=0.8, hsv_v=0.5, imgsz=416, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.02, lrf=0.0001, mask_ratio=4, max_det=300, mixup=0.3, mode=train, model=yolo11n.pt, momentum=0.937, mosaic=1.0, multi_scale=True, name=ultra_rapid_60, nbs=64, nms=False, opset=None, optimize=False, optimizer=AdamW, overlap_mask=True, patience=20, perspective=0.0, plots=False, pose=12.0, pretrained=True, profile=False, project=v3_fizzi_rapid, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=v3_fizzi_rapid\ultra_rapid_60, save_frames=False, save_json=False, save_period=10, save_txt=False, scale=0.8, seed=0, shear=2.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0.15, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=3, warmup_momentum=0.8, weight_decay=0.0001, workers=8, workspace=None
Overriding model.yaml nc=80 with nc=12

                   from  n    params  module                                       arguments
  0                  -1  1       464  ultralytics.nn.modules.conv.Conv             [3, 16, 3, 2]
  1                  -1  1      4672  ultralytics.nn.modules.conv.Conv             [16, 32, 3, 2]
  2                  -1  1      6640  ultralytics.nn.modules.block.C3k2            [32, 64, 1, False, 0.25]      
  3                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]
  4                  -1  1     26080  ultralytics.nn.modules.block.C3k2            [64, 128, 1, False, 0.25]       
  5                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]
  6                  -1  1     87040  ultralytics.nn.modules.block.C3k2            [128, 128, 1, True]
  7                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]
  8                  -1  1    346112  ultralytics.nn.modules.block.C3k2            [256, 256, 1, True]
  9                  -1  1    164608  ultralytics.nn.modules.block.SPPF            [256, 256, 5]
 10                  -1  1    249728  ultralytics.nn.modules.block.C2PSA           [256, 256, 1]
 11                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 12             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 13                  -1  1    111296  ultralytics.nn.modules.block.C3k2            [384, 128, 1, False]
 14                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 15             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 16                  -1  1     32096  ultralytics.nn.modules.block.C3k2            [256, 64, 1, False]
 17                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]
 18            [-1, 13]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 19                  -1  1     86720  ultralytics.nn.modules.block.C3k2            [192, 128, 1, False]
 20                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]
 21            [-1, 10]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 22                  -1  1    378880  ultralytics.nn.modules.block.C3k2            [384, 256, 1, True]
 23        [16, 19, 22]  1    433012  ultralytics.nn.modules.head.Detect           [12, [64, 128, 256]]
YOLO11n summary: 181 layers, 2,592,180 parameters, 2,592,164 gradients, 6.5 GFLOPs

Transferred 448/499 items from pretrained weights
Freezing layer 'model.23.dfl.conv.weight'
AMP: running Automatic Mixed Precision (AMP) checks...
AMP: checks passed 
train: Fast image access  (ping: 0.00.0 ms, read: 1013.9433.7 MB/s, size: 81.7 KB)
train: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v3_fizzi_rapid_dataset\train\labels.cache... 6
albumentations: Blur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))
val: Fast image access  (ping: 0.10.0 ms, read: 619.8143.4 MB/s, size: 89.0 KB)
val: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\v3_fizzi_rapid_dataset\val\labels.cache... 8 ima
optimizer: AdamW(lr=0.02, momentum=0.937) with parameter groups 81 weight(decay=0.0), 88 weight(decay=0.0001), 87 bias(decay=0.0)
Image sizes 416 train, 416 val
Using 8 dataloader workers
Logging results to v3_fizzi_rapid\ultra_rapid_60
Starting training for 60 epochs...

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       1/60      4.46G      3.503      12.83      1.751         74        224: 100%|██████████| 20/20 [00:06<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8          0          0          0          0

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       2/60      4.35G      3.076      8.453      1.617         64        608: 100%|██████████| 20/20 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8          0          0          0          0

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       3/60         5G      3.032      8.305      1.609         72        352: 100%|██████████| 20/20 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8          0          0          0          0

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       4/60      4.42G      2.919      7.747        1.5         40        480: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8          0          0          0          0

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       5/60      4.08G      2.781      7.663      1.489         46        480: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8          0          0          0          0

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       6/60      3.93G      2.833      7.464      1.381         73        224: 100%|██████████| 20/20 [00:03<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8          0          0          0          0

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       7/60      5.04G      2.589      7.498       1.45         67        320: 100%|██████████| 20/20 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8          0          0          0          0

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       8/60      5.33G      2.627      7.138      1.416         64        576: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.481      0.143      0.118      0.064

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       9/60      5.04G      2.641      7.122      1.378         58        480: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.189      0.286      0.066     0.0463

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      10/60      4.07G      2.657      6.832      1.412         56        512: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8     0.0164      0.786     0.0394     0.0198
 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      11/60      4.69G      2.695      6.638      1.371         84        320: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.299      0.143     0.0745     0.0352

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      12/60      4.93G      2.702      6.722      1.376         69        608: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.783      0.143      0.231      0.164

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      13/60      5.47G      2.524      6.529      1.402         72        288: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.618      0.143     0.0957     0.0636

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      14/60      4.22G      2.482      6.246      1.353         61        576: 100%|██████████| 20/20 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.158      0.286     0.0417     0.0259

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      15/60      4.52G      2.452      6.254       1.33         73        256: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8       0.65      0.286      0.156     0.0978

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      16/60      4.51G      2.329      6.113      1.342         56        544: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8       0.99      0.143      0.257       0.11

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      17/60       4.4G        2.2      5.785      1.313         74        288: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.379      0.429      0.212      0.111

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      18/60      5.08G        2.2       5.91       1.31         81        480: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.632      0.143     0.0956     0.0551

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      19/60      3.87G       2.36      5.968      1.295         78        512: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.697      0.286       0.25      0.125

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      20/60      4.15G      2.375      5.728      1.289         74        416: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.783      0.143      0.158        0.1

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      21/60      4.84G      2.325      5.714      1.313         64        224: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.979      0.143      0.216      0.119

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      22/60      4.42G      2.148      5.596      1.322         65        416: 100%|██████████| 20/20 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.492      0.429      0.267      0.184

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      23/60      4.39G      2.162      5.444      1.276         63        576: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.954      0.143      0.253      0.169

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      24/60      3.51G      2.211      5.646      1.241         58        288: 100%|██████████| 20/20 [00:03<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8          0          0          0          0

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      25/60      4.34G      2.148       5.52      1.317         84        352: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8          0          0          0          0

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      26/60      4.77G      2.168      5.365      1.271         56        224: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8       0.69      0.429      0.261       0.17

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      27/60      3.89G      2.134      5.299      1.292         80        288: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.372       0.45      0.231      0.135

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      28/60      5.45G       2.14      5.184      1.278         48        416: 100%|██████████| 20/20 [00:04<00:00 
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.682      0.286       0.25       0.17

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      29/60       4.4G      2.154      5.089      1.267         61        480: 100%|██████████| 20/20 [00:04<00:00 
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.309      0.143      0.173      0.122

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      30/60       3.9G      1.935      5.167      1.284         63        544: 100%|██████████| 20/20 [00:05<00:00 
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.371      0.406      0.225      0.146

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      31/60      4.36G      2.114      5.116      1.251         59        256: 100%|██████████| 20/20 [00:04<00:00 
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.309      0.347       0.33      0.205

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      32/60      4.83G      1.962      4.855      1.233         77        192: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.323      0.571      0.297       0.18

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      33/60      4.48G      1.905       4.85      1.244         67        544: 100%|██████████| 20/20 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.152      0.494      0.307      0.201

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      34/60      4.81G      1.875      5.002      1.277         63        512: 100%|██████████| 20/20 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.102      0.429      0.227      0.127

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      35/60      4.27G      1.904      4.766       1.23         60        608: 100%|██████████| 20/20 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.601      0.429      0.317      0.155

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      36/60      4.35G      1.896      4.688      1.229         54        256: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.156      0.714      0.335      0.221

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      37/60      4.45G      1.924       4.74      1.216         58        576: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8      0.249      0.429      0.252      0.177

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      38/60      4.31G      1.901      4.778      1.227         72        256: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.221      0.475      0.221      0.148

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      39/60      5.51G      1.816      4.674      1.247         58        576: 100%|██████████| 20/20 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8     0.0884      0.429      0.353      0.259

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      40/60      4.34G      1.912      4.678      1.232         82        224: 100%|██████████| 20/20 [00:05<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.582      0.429      0.433      0.274

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      41/60      4.79G      1.862      4.499      1.206         77        416: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8      0.465      0.571      0.446      0.283

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      42/60      4.72G      1.902      4.546       1.22         57        416: 100%|██████████| 20/20 [00:03<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00 
                   all          8          8       0.31      0.429      0.343      0.259

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      43/60      4.41G      1.874      4.564      1.194         69        608: 100%|██████████| 20/20 [00:04<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00
                   all          8          8       0.14      0.402      0.343      0.231
