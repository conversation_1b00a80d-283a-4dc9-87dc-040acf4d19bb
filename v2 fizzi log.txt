PS C:\Users\<USER>\OneDrive\Desktop\fizzi chess> python simple_breakthrough_v2.py
🚀 SIMPLE BREAKTHROUGH V2 FIZZI
🎯 Challenge: 99% mAP50 + <0.1% knowledge loss
==================================================
🎯 SIMPLE BREAKTHROUGH V2 FIZZI
📊 Target fizzi mAP50: 99.0%
🧠 Max knowledge loss: 0.1%
🤖 Base model: best.pt
🚀 SIMPLE BREAKTHROUGH PIPELINE
==================================================
✅ Dataset configuration created
🔒 Conservative Training (Knowledge Preservation)
New https://pypi.org/project/ultralytics/8.3.148 available  Update with 'pip install -U ultralytics'
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)
engine\trainer: agnostic_nms=False, amp=True, augment=False, auto_augment=randaugment, batch=4, bgr=0.0, box=7.5, cache=False, cfg=None, classes=None, close_mosaic=10, cls=0.5, conf=None, copy_paste=0.0, copy_paste_mode=flip, cos_lr=False, cutmix=0.0, data=simple_breakthrough_dataset.yaml, degrees=0.0, deterministic=True, device=None, dfl=1.5, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=30, erasing=0.4, exist_ok=True, fliplr=0.5, flipud=0.0, format=torchscript, fraction=1.0, freeze=[0, 1, 2, 3, 4, 5], half=False, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, imgsz=416, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=1e-05, lrf=0.01, mask_ratio=4, max_det=300, mixup=0.0, mode=train, model=best.pt, momentum=0.937, mosaic=1.0, multi_scale=False, name=conservative, nbs=64, nms=False, opset=None, optimize=False, optimizer=AdamW, overlap_mask=True, patience=15, perspective=0.0, plots=False, pose=12.0, pretrained=True, profile=False, project=simple_breakthrough, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=simple_breakthrough\conservative, save_frames=False, save_json=False, save_period=-1, save_txt=False, scale=0.5, seed=0, shear=0.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0.1, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=3, warmup_momentum=0.8, weight_decay=0.0001, workers=8, workspace=None

                   from  n    params  module                                       arguments        

  0                  -1  1       464  ultralytics.nn.modules.conv.Conv             [3, 16, 3, 2]    

  1                  -1  1      4672  ultralytics.nn.modules.conv.Conv             [16, 32, 3, 2]   

  2                  -1  1      6640  ultralytics.nn.modules.block.C3k2            [32, 64, 1, False, 0.25]
  3                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]   

  4                  -1  1     26080  ultralytics.nn.modules.block.C3k2            [64, 128, 1, False, 0.25]
  5                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2] 

  6                  -1  1     87040  ultralytics.nn.modules.block.C3k2            [128, 128, 1, True]
  7                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2] 

  8                  -1  1    346112  ultralytics.nn.modules.block.C3k2            [256, 256, 1, True]
  9                  -1  1    164608  ultralytics.nn.modules.block.SPPF            [256, 256, 5]    

 10                  -1  1    249728  ultralytics.nn.modules.block.C2PSA           [256, 256, 1]    

 11                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 12             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]              

 13                  -1  1    111296  ultralytics.nn.modules.block.C3k2            [384, 128, 1, False]
 14                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 15             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]              

 16                  -1  1     32096  ultralytics.nn.modules.block.C3k2            [256, 64, 1, False]
 17                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]   

 18            [-1, 13]  1         0  ultralytics.nn.modules.conv.Concat           [1]              

 19                  -1  1     86720  ultralytics.nn.modules.block.C3k2            [192, 128, 1, False]
 20                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2] 

 21            [-1, 10]  1         0  ultralytics.nn.modules.conv.Concat           [1]              

 22                  -1  1    378880  ultralytics.nn.modules.block.C3k2            [384, 256, 1, True]
 23        [16, 19, 22]  1    433012  ultralytics.nn.modules.head.Detect           [12, [64, 128, 256]]
YOLO11n summary: 181 layers, 2,592,180 parameters, 2,592,164 gradients, 6.5 GFLOPs

Transferred 499/499 items from pretrained weights
Freezing layer 'model.0.conv.weight'
Freezing layer 'model.0.bn.weight'
Freezing layer 'model.0.bn.bias'
Freezing layer 'model.1.conv.weight'
Freezing layer 'model.1.bn.weight'
Freezing layer 'model.1.bn.bias'
Freezing layer 'model.2.cv1.conv.weight'
Freezing layer 'model.2.cv1.bn.weight'
Freezing layer 'model.2.cv1.bn.bias'
Freezing layer 'model.2.cv2.conv.weight'
Freezing layer 'model.2.cv2.bn.weight'
Freezing layer 'model.2.cv2.bn.bias'
Freezing layer 'model.2.m.0.cv1.conv.weight'
Freezing layer 'model.2.m.0.cv1.bn.weight'
Freezing layer 'model.2.m.0.cv1.bn.bias'
Freezing layer 'model.2.m.0.cv2.conv.weight'
Freezing layer 'model.2.m.0.cv2.bn.weight'
Freezing layer 'model.2.m.0.cv2.bn.bias'
Freezing layer 'model.3.conv.weight'
Freezing layer 'model.3.bn.weight'
Freezing layer 'model.3.bn.bias'
Freezing layer 'model.4.cv1.conv.weight'
Freezing layer 'model.4.cv1.bn.weight'
Freezing layer 'model.4.cv1.bn.bias'
Freezing layer 'model.4.cv2.conv.weight'
Freezing layer 'model.4.cv2.bn.weight'
Freezing layer 'model.4.cv2.bn.bias'
Freezing layer 'model.4.m.0.cv1.conv.weight'
Freezing layer 'model.4.m.0.cv1.bn.weight'
Freezing layer 'model.4.m.0.cv1.bn.bias'
Freezing layer 'model.4.m.0.cv2.conv.weight'
Freezing layer 'model.4.m.0.cv2.bn.weight'
Freezing layer 'model.4.m.0.cv2.bn.bias'
Freezing layer 'model.5.conv.weight'
Freezing layer 'model.5.bn.weight'
Freezing layer 'model.5.bn.bias'
Freezing layer 'model.23.dfl.conv.weight'
AMP: running Automatic Mixed Precision (AMP) checks...
AMP: checks passed
train: Fast image access  (ping: 0.00.0 ms, read: 1080.4385.2 MB/s, size: 83.7 KB)
train: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\fizzi_416x416_dataset\labels.ca
albumentations: Blur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))
val: Fast image access  (ping: 0.10.0 ms, read: 224.676.1 MB/s, size: 78.4 KB)
val: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\fizzi_416x416_dataset\labels.cach
optimizer: AdamW(lr=1e-05, momentum=0.937) with parameter groups 81 weight(decay=0.0), 88 weight(decay=0.0001), 87 bias(decay=0.0)
Image sizes 416 train, 416 val
Using 8 dataloader workers
Logging results to simple_breakthrough\conservative
Starting training for 30 epochs...

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       1/30     0.266G      1.615      19.69     0.8767          7        416: 100%|██████████| 12/
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|████
                   all         48         48      0.143      0.667      0.139      0.096

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       2/30     0.402G      1.301       4.59     0.8809          5        416: 100%|██████████| 12/
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|████
                   all         48         48     0.0386      0.396     0.0559      0.041

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       3/30     0.684G      1.239       3.95     0.8487          6        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00 
                   all         48         48      0.295      0.283     0.0896     0.0666

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       4/30     0.684G      1.189      3.837     0.8647          2        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00
                   all         48         48       0.48      0.232      0.127     0.0923

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       5/30     0.684G      1.191      3.283     0.8349          7        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00 
                   all         48         48       0.48      0.292      0.161      0.116

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       6/30     0.684G       1.19      3.315      0.848          6        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00
                   all         48         48      0.302      0.312      0.168      0.124

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       7/30     0.684G      1.277      3.558     0.8688          5        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00
                   all         48         48      0.302      0.312      0.168      0.124

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       8/30     0.684G      1.172      3.481     0.8718          7        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00
                   all         48         48      0.317      0.312      0.152      0.113

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       9/30     0.684G      1.109       3.71     0.8577          7        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00
                   all         48         48      0.336      0.361      0.138      0.102

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      10/30     0.684G      1.165       3.35     0.8735          5        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00 
                   all         48         48      0.397      0.302      0.182      0.137

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      11/30     0.684G      1.126      3.564     0.8024          7        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00 
                   all         48         48      0.397      0.302      0.182      0.137

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      12/30     0.684G      1.132      3.581      0.856          5        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00 
                   all         48         48      0.393      0.292      0.163      0.124

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      13/30     0.684G       1.11      3.434     0.8743          5        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00
                   all         48         48      0.466      0.271      0.174      0.134

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      14/30     0.684G      1.124       3.42     0.8204          5        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00 
                   all         48         48      0.466      0.271      0.171       0.13

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      15/30     0.684G      1.095      3.437     0.8695          4        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00 
                   all         48         48      0.466      0.271      0.171       0.13

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      16/30     0.684G      1.105      3.425     0.8571          4        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00 
                   all         48         48      0.468      0.289      0.172      0.131

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      17/30     0.684G      1.175      3.117     0.8849          6        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00
                   all         48         48      0.416      0.342      0.173       0.13

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      18/30     0.684G      1.213      3.348     0.8628          5        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00 
                   all         48         48      0.474      0.312      0.169      0.128

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      19/30     0.684G      1.082      3.502     0.8236          5        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00 
                   all         48         48      0.474      0.312      0.169      0.128

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      20/30     0.684G      1.217      3.357     0.8688          6        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00 
                   all         48         48      0.417      0.368      0.182      0.139
Closing dataloader mosaic
albumentations: Blur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      21/30     0.684G     0.9971      3.119     0.8631          4        416: 100%|██████████| 12/12 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00
                   all         48         48      0.408      0.394      0.171      0.132

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      22/30     0.684G     0.9991      3.155     0.8289          4        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00 
                   all         48         48      0.412      0.377      0.147      0.114

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      23/30     0.684G     0.9691      3.055     0.8557          4        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00
                   all         48         48      0.412      0.377      0.147      0.114

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      24/30     0.684G      1.033      3.289     0.8173          4        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00 
                   all         48         48      0.409      0.382      0.186      0.143

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      25/30     0.684G     0.9595      3.071     0.8427          4        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00 
                   all         48         48      0.405      0.375      0.182       0.14

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      26/30     0.684G      1.037      3.003     0.8541          4        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00 
                   all         48         48      0.409      0.406      0.185      0.144

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      27/30     0.684G      1.104      3.154     0.8648          4        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00
                   all         48         48      0.409      0.406      0.185      0.144

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      28/30     0.684G      1.151      3.582     0.8922          4        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00
                   all         48         48      0.417      0.421      0.163      0.125

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      29/30     0.684G       1.02      3.315     0.8363          4        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00 
                   all         48         48       0.41      0.417      0.149      0.116

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      30/30     0.684G      1.045      3.114     0.8714          4        416: 100%|██████████| 12/12 [00:00<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00
                   all         48         48       0.42       0.45      0.178      0.138

30 epochs completed in 0.018 hours.
Optimizer stripped from simple_breakthrough\conservative\weights\last.pt, 5.4MB
Optimizer stripped from simple_breakthrough\conservative\weights\best.pt, 5.4MB

Validating simple_breakthrough\conservative\weights\best.pt...
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)      
YOLO11n summary (fused): 100 layers, 2,584,492 parameters, 0 gradients, 6.3 GFLOPs
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 6/6 [00
                   all         48         48      0.409      0.404      0.148      0.114
            white_pawn          4          4          1          0     0.0437      0.035
          white_knight          4          4      0.106          1      0.259      0.202
          white_bishop          4          4      0.035       0.25     0.0772     0.0549
            white_rook          4          4      0.128       0.75      0.123     0.0978
           white_queen          4          4      0.111      0.595      0.191      0.149
            white_king          4          4      0.162        0.5      0.231       0.18
            black_pawn          4          4          1          0          0          0
          black_knight          4          4       0.14       0.75      0.156      0.116
          black_bishop          4          4          1          0     0.0609      0.048
            black_rook          4          4      0.131       0.25      0.199      0.161
           black_queen          4          4          1          0      0.295      0.229
            black_king          4          4     0.0923       0.75      0.135      0.101
Speed: 0.1ms preprocess, 1.8ms inference, 0.0ms loss, 1.1ms postprocess per image
🧠 Conservative Knowledge Loss: 159.222%
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)
YOLO11n summary (fused): 100 layers, 2,584,492 parameters, 0 gradients, 6.3 GFLOPs
val: Fast image access  (ping: 0.00.0 ms, read: 1167.2280.0 MB/s, size: 91.2 KB)
val: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\fizzi_416x416_dataset\labels.cache... 48 images,
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 48/48 [
                   all         48         48      0.407      0.396      0.162      0.127
Speed: 0.5ms preprocess, 11.6ms inference, 0.0ms loss, 1.3ms postprocess per image
⚖️ Balanced Training
New https://pypi.org/project/ultralytics/8.3.148 available  Update with 'pip install -U ultralytics'
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)
engine\trainer: agnostic_nms=False, amp=True, augment=True, auto_augment=randaugment, batch=3, bgr=0.0, box=7.5, cache=False, cfg=None, classes=None, close_mosaic=10, cls=0.5, conf=None, copy_paste=0.0, copy_paste_mode=flip, cos_lr=False, cutmix=0.0, data=simple_breakthrough_dataset.yaml, degrees=0.0, deterministic=True, device=None, dfl=1.5, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=50, erasing=0.4, exist_ok=True, fliplr=0.5, flipud=0.0, format=torchscript, fraction=1.0, freeze=[0, 1, 2], half=False, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, imgsz=416, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.0001, lrf=0.01, mask_ratio=4, max_det=300, mixup=0.0, mode=train, model=best.pt, momentum=0.937, mosaic=1.0, multi_scale=False, name=balanced, nbs=64, nms=False, opset=None, optimize=False, optimizer=AdamW, overlap_mask=True, patience=20, perspective=0.0, plots=False, pose=12.0, pretrained=True, profile=False, project=simple_breakthrough, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=simple_breakthrough\balanced, save_frames=False, save_json=False, save_period=-1, save_txt=False, scale=0.5, seed=0, shear=0.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0.1, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=5, warmup_momentum=0.8, weight_decay=0.0005, workers=8, workspace=None

                   from  n    params  module                                       arguments
  0                  -1  1       464  ultralytics.nn.modules.conv.Conv             [3, 16, 3, 2]
  1                  -1  1      4672  ultralytics.nn.modules.conv.Conv             [16, 32, 3, 2]
  2                  -1  1      6640  ultralytics.nn.modules.block.C3k2            [32, 64, 1, False, 0.25]        
  3                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]
  4                  -1  1     26080  ultralytics.nn.modules.block.C3k2            [64, 128, 1, False, 0.25]       
  5                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]
  6                  -1  1     87040  ultralytics.nn.modules.block.C3k2            [128, 128, 1, True]
  7                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]
  8                  -1  1    346112  ultralytics.nn.modules.block.C3k2            [256, 256, 1, True]
  9                  -1  1    164608  ultralytics.nn.modules.block.SPPF            [256, 256, 5]
 10                  -1  1    249728  ultralytics.nn.modules.block.C2PSA           [256, 256, 1]
 11                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 12             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 13                  -1  1    111296  ultralytics.nn.modules.block.C3k2            [384, 128, 1, False]
 14                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 15             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 16                  -1  1     32096  ultralytics.nn.modules.block.C3k2            [256, 64, 1, False]
 17                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]
 18            [-1, 13]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 19                  -1  1     86720  ultralytics.nn.modules.block.C3k2            [192, 128, 1, False]
 20                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]
 21            [-1, 10]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 22                  -1  1    378880  ultralytics.nn.modules.block.C3k2            [384, 256, 1, True]
 23        [16, 19, 22]  1    433012  ultralytics.nn.modules.head.Detect           [12, [64, 128, 256]]
YOLO11n summary: 181 layers, 2,592,180 parameters, 2,592,164 gradients, 6.5 GFLOPs

Transferred 499/499 items from pretrained weights
Freezing layer 'model.0.conv.weight'
Freezing layer 'model.0.bn.weight'
Freezing layer 'model.0.bn.bias'
Freezing layer 'model.1.conv.weight'
Freezing layer 'model.1.bn.weight'
Freezing layer 'model.1.bn.bias'
Freezing layer 'model.2.cv1.conv.weight'
Freezing layer 'model.2.cv1.bn.weight'
Freezing layer 'model.2.cv1.bn.bias'
Freezing layer 'model.2.cv2.conv.weight'
Freezing layer 'model.2.cv2.bn.weight'
Freezing layer 'model.2.cv2.bn.bias'
Freezing layer 'model.2.m.0.cv1.conv.weight'
Freezing layer 'model.2.m.0.cv1.bn.weight'
Freezing layer 'model.2.m.0.cv1.bn.bias'
Freezing layer 'model.2.m.0.cv2.conv.weight'
Freezing layer 'model.2.m.0.cv2.bn.weight'
Freezing layer 'model.2.m.0.cv2.bn.bias'
Freezing layer 'model.23.dfl.conv.weight'
AMP: running Automatic Mixed Precision (AMP) checks...
AMP: checks passed 
train: Fast image access  (ping: 0.00.0 ms, read: 1122.4354.9 MB/s, size: 83.7 KB)
train: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\fizzi_416x416_dataset\labels.cache... 48 image 
albumentations: Blur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))
val: Fast image access  (ping: 0.10.0 ms, read: 424.4139.8 MB/s, size: 78.4 KB)
val: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\fizzi_416x416_dataset\labels.cache... 48 images,
optimizer: AdamW(lr=0.0001, momentum=0.937) with parameter groups 81 weight(decay=0.0), 88 weight(decay=0.0004921875), 87 bias(decay=0.0)
Image sizes 416 train, 416 val
Using 8 dataloader workers
Logging results to simple_breakthrough\balanced
Starting training for 50 epochs...

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       1/50     0.289G      1.635      23.58     0.9161          3        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all         48         48      0.202      0.521      0.164      0.111

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       2/50     0.346G      1.527      6.305     0.8982          1        416: 100%|██████████| 16/16 [00:03<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all         48         48     0.0356      0.375     0.0478     0.0309

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       3/50      0.65G      1.413      4.291     0.8099          5        416: 100%|██████████| 16/16 [00:03<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all         48         48     0.0565      0.333     0.0929     0.0636

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       4/50      0.65G      1.386      4.282     0.8904          3        416: 100%|██████████| 16/16 [00:03<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all         48         48      0.139      0.375     0.0997     0.0706

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       5/50      0.65G      1.223      3.856     0.8389          7        416: 100%|██████████| 16/16 [00:03<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all         48         48      0.395      0.261      0.114     0.0826

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       6/50      0.65G      1.277      3.985     0.8526          3        416: 100%|██████████| 16/16 [00:03<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all         48         48      0.404      0.292      0.162      0.118

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       7/50      0.65G      1.241      3.908      0.849          2        416: 100%|██████████| 16/16 [00:03<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all         48         48      0.404      0.292      0.162      0.118

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       8/50      0.65G      1.056      3.476     0.8316          5        416: 100%|██████████| 16/16 [00:03<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all         48         48      0.496      0.354      0.158       0.12

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       9/50      0.65G      1.307      4.201     0.8594          3        416: 100%|██████████| 16/16 [00:03<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all         48         48      0.411      0.397      0.178      0.136

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      10/50      0.65G      1.181      3.564     0.8672          2        416: 100%|██████████| 16/16 [00:03<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all         48         48      0.425      0.429      0.159      0.122

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      11/50      0.65G      1.041      3.532     0.8327          5        416: 100%|██████████| 16/16 [00:03<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all         48         48      0.425      0.429      0.159      0.122

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      12/50      0.65G      1.101      3.391     0.8281          4        416: 100%|██████████| 16/16 [00:03<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all         48         48       0.42      0.438      0.176      0.136

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      13/50      0.65G      1.079      3.224       0.85          3        416: 100%|██████████| 16/16 [00:02<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all         48         48      0.422      0.438      0.202      0.159

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      14/50      0.65G      1.177      3.253      0.878          4        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all         48         48      0.332      0.458      0.163      0.129

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      15/50      0.65G      1.117      3.276     0.8492          2        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all         48         48      0.332      0.458      0.163      0.129

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      16/50      0.65G      0.986      3.008     0.8506          4        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all         48         48      0.332       0.48      0.177      0.143

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      17/50      0.65G      1.077      3.258     0.8751          4        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all         48         48      0.332      0.479      0.186      0.147

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      18/50      0.65G      1.135      3.524     0.8257          5        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all         48         48      0.335      0.521      0.162      0.129

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      19/50      0.65G      1.057      3.169     0.8299          4        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all         48         48      0.335      0.521      0.162      0.129

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      20/50      0.65G      1.038      3.302     0.8415          4        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all         48         48      0.353      0.497      0.164       0.13

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      21/50      0.65G      1.118      3.043     0.8857          5        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all         48         48       0.36      0.454       0.17      0.136

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      22/50      0.65G      1.069      3.286     0.8679          3        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all         48         48       0.26      0.596       0.16      0.128

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      23/50      0.65G      1.046       3.26     0.8377          3        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all         48         48      0.358      0.474       0.16      0.127

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      24/50      0.65G     0.9585      3.175     0.8289          3        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all         48         48      0.358      0.474       0.16      0.127

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      25/50      0.65G     0.9496      2.914     0.8332          5        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all         48         48      0.272      0.667      0.161      0.128

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      26/50      0.65G      1.077      2.997     0.8414          5        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all         48         48      0.275      0.639      0.182      0.143

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      27/50      0.65G      1.092      3.175     0.7956          5        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all         48         48      0.278      0.667      0.162      0.129

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      28/50      0.65G       1.03      2.939     0.8363          4        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all         48         48      0.278      0.667      0.162      0.129

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      29/50      0.65G      1.018      2.878     0.8461          5        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all         48         48      0.285      0.667      0.169      0.135

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      30/50      0.65G      1.012      2.926     0.8252          4        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all         48         48      0.288      0.667      0.161      0.129

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      31/50      0.65G      1.059      3.031     0.8774          3        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all         48         48      0.289      0.657       0.16      0.127

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      32/50      0.65G       1.09      3.247     0.8733          3        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all         48         48      0.289      0.657       0.16      0.127

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      33/50      0.65G     0.9549      3.524     0.8229          2        416: 100%|██████████| 16/16 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all         48         48      0.284      0.646      0.178      0.144
EarlyStopping: Training stopped early as no improvement observed in last 20 epochs. Best results observed at epoch 13, best model saved as best.pt.
To update EarlyStopping(patience=20) pass a new patience value, i.e. `patience=300` or use `patience=0` to disable EarlyStopping.

33 epochs completed in 0.029 hours.
Optimizer stripped from simple_breakthrough\balanced\weights\last.pt, 5.4MB
Optimizer stripped from simple_breakthrough\balanced\weights\best.pt, 5.4MB

Validating simple_breakthrough\balanced\weights\best.pt...
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)      
YOLO11n summary (fused): 100 layers, 2,584,492 parameters, 0 gradients, 6.3 GFLOPs
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all         48         48      0.515      0.417      0.169      0.124
            white_pawn          4          4          1          0     0.0325     0.0212
          white_knight          4          4      0.152          1      0.339      0.255
          white_bishop          4          4          1          0     0.0658     0.0477
            white_rook          4          4     0.0955       0.25      0.143      0.102
           white_queen          4          4      0.217          1      0.265      0.195
            white_king          4          4          1          0      0.075     0.0563
            black_pawn          4          4          1          0     0.0326      0.018
          black_knight          4          4      0.186       0.75       0.22      0.144
          black_bishop          4          4          1          0     0.0613     0.0474
            black_rook          4          4      0.181          1      0.403      0.328
           black_queen          4          4      0.261       0.25      0.279      0.194
            black_king          4          4     0.0927       0.75      0.108     0.0779
Speed: 0.2ms preprocess, 10.1ms inference, 0.0ms loss, 1.3ms postprocess per image
🧠 Balanced Knowledge Loss: 116.377%
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)
YOLO11n summary (fused): 100 layers, 2,584,492 parameters, 0 gradients, 6.3 GFLOPs
val: Fast image access  (ping: 0.00.0 ms, read: 1261.5610.0 MB/s, size: 91.2 KB)
val: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\fizzi_416x416_dataset\labels.cache... 48 images,
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 48/48 [
                   all         48         48      0.422      0.438      0.187      0.147
Speed: 0.5ms preprocess, 10.9ms inference, 0.0ms loss, 1.3ms postprocess per image
🚀 Aggressive Training (High Performance)
New https://pypi.org/project/ultralytics/8.3.148 available  Update with 'pip install -U ultralytics'
WARNING 'label_smoothing' is deprecated and will be removed in in the future.
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)      
engine\trainer: agnostic_nms=False, amp=True, augment=True, auto_augment=randaugment, batch=2, bgr=0.0, box=7.5, cache=False, cfg=None, classes=None, close_mosaic=10, cls=0.5, conf=None, copy_paste=0.0, copy_paste_mode=flip, cos_lr=False, cutmix=0.0, data=simple_breakthrough_dataset.yaml, degrees=0.0, deterministic=True, device=None, dfl=1.5, dnn=False, dropout=0.1, dynamic=False, embed=None, epochs=80, erasing=0.4, exist_ok=True, fliplr=0.5, flipud=0.0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, imgsz=416, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.0005, lrf=0.01, mask_ratio=4, max_det=300, mixup=0.0, mode=train, model=best.pt, momentum=0.937, mosaic=1.0, multi_scale=True, name=aggressive, nbs=64, nms=False, opset=None, optimize=False, optimizer=AdamW, overlap_mask=True, patience=25, perspective=0.0, plots=False, pose=12.0, pretrained=True, profile=False, project=simple_breakthrough, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=simple_breakthrough\aggressive, save_frames=False, save_json=False, save_period=-1, save_txt=False, scale=0.5, seed=0, shear=0.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0.1, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=5, warmup_momentum=0.8, weight_decay=0.001, workers=8, workspace=None

                   from  n    params  module                                       arguments
  0                  -1  1       464  ultralytics.nn.modules.conv.Conv             [3, 16, 3, 2]
  1                  -1  1      4672  ultralytics.nn.modules.conv.Conv             [16, 32, 3, 2]
  2                  -1  1      6640  ultralytics.nn.modules.block.C3k2            [32, 64, 1, False, 0.25]        
  3                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]
  4                  -1  1     26080  ultralytics.nn.modules.block.C3k2            [64, 128, 1, False, 0.25]       
  5                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]
  6                  -1  1     87040  ultralytics.nn.modules.block.C3k2            [128, 128, 1, True]
  7                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]
  8                  -1  1    346112  ultralytics.nn.modules.block.C3k2            [256, 256, 1, True]
  9                  -1  1    164608  ultralytics.nn.modules.block.SPPF            [256, 256, 5]
 10                  -1  1    249728  ultralytics.nn.modules.block.C2PSA           [256, 256, 1]
 11                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 12             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 13                  -1  1    111296  ultralytics.nn.modules.block.C3k2            [384, 128, 1, False]
 14                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']
 15             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 16                  -1  1     32096  ultralytics.nn.modules.block.C3k2            [256, 64, 1, False]
 17                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]
 18            [-1, 13]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 19                  -1  1     86720  ultralytics.nn.modules.block.C3k2            [192, 128, 1, False]
 20                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]
 21            [-1, 10]  1         0  ultralytics.nn.modules.conv.Concat           [1]
 22                  -1  1    378880  ultralytics.nn.modules.block.C3k2            [384, 256, 1, True]
 23        [16, 19, 22]  1    433012  ultralytics.nn.modules.head.Detect           [12, [64, 128, 256]]
YOLO11n summary: 181 layers, 2,592,180 parameters, 2,592,164 gradients, 6.5 GFLOPs

Transferred 499/499 items from pretrained weights
Freezing layer 'model.23.dfl.conv.weight'
AMP: running Automatic Mixed Precision (AMP) checks...
AMP: checks passed 
train: Fast image access  (ping: 0.00.0 ms, read: 723.7269.5 MB/s, size: 83.7 KB)
train: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\fizzi_416x416_dataset\labels.cache... 48 image 
albumentations: Blur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))
val: Fast image access  (ping: 0.10.0 ms, read: 718.1246.6 MB/s, size: 78.4 KB)
val: Scanning C:\Users\<USER>\OneDrive\Desktop\fizzi chess\fizzi_416x416_dataset\labels.cache... 48 images,
optimizer: AdamW(lr=0.0005, momentum=0.937) with parameter groups 81 weight(decay=0.0), 88 weight(decay=0.001), 87 bias(decay=0.0)
Image sizes 416 train, 416 val
Using 8 dataloader workers
Logging results to simple_breakthrough\aggressive
Starting training for 80 epochs...

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       1/80     0.432G      1.407      30.84     0.8031          2        448: 100%|██████████| 24/24 [00:03<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [ 
                   all         48         48      0.101      0.662      0.139     0.0928

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       2/80     0.439G      1.591      13.69     0.8457          1        608: 100%|██████████| 24/24 [00:02<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [
                   all         48         48      0.035      0.479     0.0568      0.037

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       3/80     0.619G      1.573      5.663     0.8968          4        416: 100%|██████████| 24/24 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [ 
                   all         48         48     0.0419      0.312     0.0556     0.0411

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       4/80     0.619G      1.403      4.647     0.8648          1        512: 100%|██████████| 24/24 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [
                   all         48         48      0.209      0.229     0.0891     0.0706

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       5/80     0.619G      1.767      4.778     0.8293          5        224: 100%|██████████| 24/24 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [ 
                   all         48         48      0.209      0.229     0.0891     0.0706

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       6/80     0.619G      1.159      4.517     0.8449          3        512: 100%|██████████| 24/24 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [ 
                   all         48         48      0.619      0.146      0.119     0.0892

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       7/80     0.619G      1.484      4.727     0.8013          1        352: 100%|██████████| 24/24 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [ 
                   all         48         48      0.652      0.188       0.16      0.126

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       8/80     0.619G      1.503      4.094     0.8587          5        480: 100%|██████████| 24/24 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [
                   all         48         48      0.568      0.271      0.165      0.125

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
       9/80     0.619G      1.446      3.833     0.8405          3        256: 100%|██████████| 24/24 [00:02<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [
                   all         48         48      0.568      0.271      0.165      0.125

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      10/80     0.619G      1.289      4.354     0.8196          3        608: 100%|██████████| 24/24 [00:02<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [
                   all         48         48      0.493      0.371      0.145      0.112

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      11/80     0.619G      1.093      4.473     0.7999          3        416: 100%|██████████| 24/24 [00:02<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [
                   all         48         48      0.427      0.396      0.175      0.136

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      12/80     0.619G      1.402      4.201     0.7967          2        320: 100%|██████████| 24/24 [00:02<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [
                   all         48         48      0.406      0.375      0.145      0.115

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      13/80     0.619G      1.221      4.289      0.803          3        544: 100%|██████████| 24/24 [00:02<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [ 
                   all         48         48      0.406      0.375      0.145      0.115

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      14/80     0.619G      1.168      3.482     0.8504          4        416: 100%|██████████| 24/24 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [ 
                   all         48         48      0.314      0.375      0.127     0.0985

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      15/80     0.619G      1.222      3.523     0.7881          2        480: 100%|██████████| 24/24 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [
                   all         48         48      0.323      0.412      0.141      0.106

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      16/80     0.619G      1.414        4.6     0.9227          2        512: 100%|██████████| 24/24 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [
                   all         48         48      0.321      0.412      0.152      0.115

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      17/80     0.619G      1.361      4.507     0.8406          4        448: 100%|██████████| 24/24 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [ 
                   all         48         48      0.321      0.412      0.152      0.115

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      18/80     0.619G      1.254       3.83     0.8247          4        544: 100%|██████████| 24/24 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [
                   all         48         48      0.404      0.354      0.167      0.129

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      19/80     0.619G      1.074      3.608     0.7969          3        384: 100%|██████████| 24/24 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [ 
                   all         48         48      0.257      0.438      0.199      0.145

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      20/80     0.619G      1.216      3.744     0.8011          4        288: 100%|██████████| 24/24 [00:01<00:00
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [ 
                   all         48         48      0.275      0.458      0.201      0.147