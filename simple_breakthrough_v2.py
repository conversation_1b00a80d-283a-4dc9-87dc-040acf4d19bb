#!/usr/bin/env python3
"""
Simple Breakthrough V2 Fizzi - Knowledge Preservation
Target: 99% mAP50 on fizzi + <0.1% knowledge loss
Simplified approach without matplotlib dependencies
"""

import os
import torch
import yaml
import copy
from ultralytics import YOLO

class SimpleBreakthroughV2:
    def __init__(self, base_model_path='best.pt', target_map50=0.99, max_knowledge_loss=0.001):
        self.base_model_path = base_model_path
        self.target_map50 = target_map50
        self.max_knowledge_loss = max_knowledge_loss
        
        print(f"🎯 SIMPLE BREAKTHROUGH V2 FIZZI")
        print(f"📊 Target fizzi mAP50: {target_map50*100}%")
        print(f"🧠 Max knowledge loss: {max_knowledge_loss*100}%")
        print(f"🤖 Base model: {base_model_path}")
        
        # Load base model and extract weights
        self.base_model = YOLO(base_model_path)
        self.original_weights = self.extract_weights()
        
    def extract_weights(self):
        """Extract original model weights for comparison."""
        weights = {}
        for name, param in self.base_model.model.named_parameters():
            weights[name] = param.data.clone().detach()
        return weights
    
    def calculate_knowledge_loss(self, model_path):
        """Calculate knowledge loss compared to original model."""
        if not os.path.exists(model_path):
            return 1.0
        
        try:
            current_model = YOLO(model_path)
            total_loss = 0
            total_params = 0
            
            for name, param in current_model.model.named_parameters():
                if name in self.original_weights:
                    original = self.original_weights[name]
                    current = param.data
                    
                    # Calculate relative change
                    diff = torch.norm(current - original).item()
                    norm = torch.norm(original).item()
                    
                    if norm > 0:
                        relative_change = diff / norm
                        total_loss += relative_change
                        total_params += 1
            
            avg_loss = total_loss / total_params if total_params > 0 else 0
            return avg_loss
            
        except Exception as e:
            print(f"Error calculating knowledge loss: {e}")
            return 1.0
    
    def create_dataset_config(self):
        """Create simple dataset configuration."""
        config = {
            'path': os.path.abspath('fizzi_416x416_dataset'),
            'train': 'images',
            'val': 'images',
            'nc': 12,
            'names': [
                'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
                'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
            ]
        }
        
        with open('simple_breakthrough_dataset.yaml', 'w') as f:
            yaml.dump(config, f)
        
        return 'simple_breakthrough_dataset.yaml'
    
    def conservative_training(self, dataset_yaml):
        """Conservative training to preserve knowledge."""
        print("🔒 Conservative Training (Knowledge Preservation)")
        
        model = YOLO(self.base_model_path)
        
        args = {
            'data': dataset_yaml,
            'epochs': 30,
            'imgsz': 416,
            'batch': 4,
            'lr0': 0.00001,  # Ultra-low learning rate
            'weight_decay': 0.0001,
            'warmup_epochs': 3,
            'patience': 15,
            'project': 'simple_breakthrough',
            'name': 'conservative',
            'exist_ok': True,
            'optimizer': 'AdamW',
            'freeze': [0, 1, 2, 3, 4, 5],  # Freeze many layers
            'augment': False,
            'verbose': True,
            'plots': False  # Disable plots to avoid matplotlib
        }
        
        try:
            results = model.train(**args)
            model_path = 'simple_breakthrough/conservative/weights/best.pt'
            knowledge_loss = self.calculate_knowledge_loss(model_path)
            print(f"🧠 Conservative Knowledge Loss: {knowledge_loss*100:.3f}%")
            return model_path, knowledge_loss
        except Exception as e:
            print(f"❌ Conservative training failed: {e}")
            return None, 1.0
    
    def balanced_training(self, dataset_yaml):
        """Balanced training approach."""
        print("⚖️ Balanced Training")
        
        model = YOLO(self.base_model_path)
        
        args = {
            'data': dataset_yaml,
            'epochs': 50,
            'imgsz': 416,
            'batch': 3,
            'lr0': 0.0001,
            'weight_decay': 0.0005,
            'warmup_epochs': 5,
            'patience': 20,
            'project': 'simple_breakthrough',
            'name': 'balanced',
            'exist_ok': True,
            'optimizer': 'AdamW',
            'freeze': [0, 1, 2],  # Freeze fewer layers
            'augment': True,
            'verbose': True,
            'plots': False
        }
        
        try:
            results = model.train(**args)
            model_path = 'simple_breakthrough/balanced/weights/best.pt'
            knowledge_loss = self.calculate_knowledge_loss(model_path)
            print(f"🧠 Balanced Knowledge Loss: {knowledge_loss*100:.3f}%")
            return model_path, knowledge_loss
        except Exception as e:
            print(f"❌ Balanced training failed: {e}")
            return None, 1.0
    
    def aggressive_training(self, dataset_yaml):
        """Aggressive training for high performance."""
        print("🚀 Aggressive Training (High Performance)")
        
        model = YOLO(self.base_model_path)
        
        args = {
            'data': dataset_yaml,
            'epochs': 80,
            'imgsz': 416,
            'batch': 2,
            'lr0': 0.0005,
            'weight_decay': 0.001,
            'warmup_epochs': 5,
            'patience': 25,
            'project': 'simple_breakthrough',
            'name': 'aggressive',
            'exist_ok': True,
            'optimizer': 'AdamW',
            'augment': True,
            'multi_scale': True,
            'label_smoothing': 0.1,
            'dropout': 0.1,
            'verbose': True,
            'plots': False
        }
        
        try:
            results = model.train(**args)
            model_path = 'simple_breakthrough/aggressive/weights/best.pt'
            knowledge_loss = self.calculate_knowledge_loss(model_path)
            print(f"🧠 Aggressive Knowledge Loss: {knowledge_loss*100:.3f}%")
            return model_path, knowledge_loss
        except Exception as e:
            print(f"❌ Aggressive training failed: {e}")
            return None, 1.0
    
    def evaluate_model(self, model_path, dataset_yaml):
        """Evaluate model performance."""
        if not os.path.exists(model_path):
            return 0, 1
        
        try:
            model = YOLO(model_path)
            results = model.val(
                data=dataset_yaml,
                imgsz=416,
                batch=1,
                verbose=False,
                plots=False
            )
            
            map50 = results.box.map50 if hasattr(results.box, 'map50') else 0
            knowledge_loss = self.calculate_knowledge_loss(model_path)
            
            return map50, knowledge_loss
            
        except Exception as e:
            print(f"❌ Error evaluating {model_path}: {e}")
            return 0, 1
    
    def breakthrough_pipeline(self):
        """Execute breakthrough training pipeline."""
        print("🚀 SIMPLE BREAKTHROUGH PIPELINE")
        print("=" * 50)
        
        # Create dataset
        dataset_yaml = self.create_dataset_config()
        print("✅ Dataset configuration created")
        
        # Train multiple models
        models = []
        
        # Conservative approach
        conservative_path, conservative_loss = self.conservative_training(dataset_yaml)
        if conservative_path:
            conservative_map50, _ = self.evaluate_model(conservative_path, dataset_yaml)
            models.append(('Conservative', conservative_path, conservative_map50, conservative_loss))
        
        # Balanced approach
        balanced_path, balanced_loss = self.balanced_training(dataset_yaml)
        if balanced_path:
            balanced_map50, _ = self.evaluate_model(balanced_path, dataset_yaml)
            models.append(('Balanced', balanced_path, balanced_map50, balanced_loss))
        
        # Aggressive approach
        aggressive_path, aggressive_loss = self.aggressive_training(dataset_yaml)
        if aggressive_path:
            aggressive_map50, _ = self.evaluate_model(aggressive_path, dataset_yaml)
            models.append(('Aggressive', aggressive_path, aggressive_map50, aggressive_loss))
        
        # Select best model
        return self.select_best_model(models)
    
    def select_best_model(self, models):
        """Select best model meeting constraints."""
        print("\n🏆 MODEL EVALUATION")
        print("=" * 50)
        
        valid_models = []
        
        for name, path, map50, knowledge_loss in models:
            constraint_met = knowledge_loss <= self.max_knowledge_loss
            target_met = map50 >= self.target_map50
            
            status = "✅" if constraint_met else "❌"
            target_status = "🎯" if target_met else "📈"
            
            print(f"{name}:")
            print(f"  mAP50: {map50*100:.2f}% {target_status}")
            print(f"  Knowledge Loss: {knowledge_loss*100:.3f}% {status}")
            print(f"  Constraint Met: {constraint_met}")
            
            if constraint_met:
                valid_models.append((name, path, map50, knowledge_loss))
        
        if not valid_models:
            print("\n❌ No models meet knowledge preservation constraints!")
            return None
        
        # Sort by mAP50
        valid_models.sort(key=lambda x: x[2], reverse=True)
        best_name, best_path, best_map50, best_loss = valid_models[0]
        
        print(f"\n🏆 BEST MODEL: {best_name}")
        print(f"📁 Path: {best_path}")
        print(f"🎯 mAP50: {best_map50*100:.2f}%")
        print(f"🧠 Knowledge Loss: {best_loss*100:.3f}%")
        
        # Copy to final location
        final_path = 'simple_breakthrough_v2_best.pt'
        import shutil
        shutil.copy2(best_path, final_path)
        
        # Check success
        target_achieved = best_map50 >= self.target_map50
        constraint_satisfied = best_loss <= self.max_knowledge_loss
        
        if target_achieved and constraint_satisfied:
            print(f"\n🎉 BREAKTHROUGH SUCCESS!")
            print(f"✅ Target: {best_map50*100:.2f}% >= {self.target_map50*100}%")
            print(f"✅ Constraint: {best_loss*100:.3f}% <= {self.max_knowledge_loss*100}%")
        else:
            print(f"\n📈 Partial Success")
            print(f"Target: {best_map50*100:.2f}% / {self.target_map50*100}%")
            print(f"Constraint: {best_loss*100:.3f}% / {self.max_knowledge_loss*100}%")
        
        return {
            'model_path': final_path,
            'name': best_name,
            'map50': best_map50,
            'knowledge_loss': best_loss,
            'target_achieved': target_achieved,
            'constraint_satisfied': constraint_satisfied
        }

def main():
    """Main execution."""
    print("🚀 SIMPLE BREAKTHROUGH V2 FIZZI")
    print("🎯 Challenge: 99% mAP50 + <0.1% knowledge loss")
    print("=" * 50)
    
    trainer = SimpleBreakthroughV2(
        base_model_path='best.pt',
        target_map50=0.99,
        max_knowledge_loss=0.001
    )
    
    try:
        result = trainer.breakthrough_pipeline()
        
        if result:
            print(f"\n🎉 TRAINING COMPLETED!")
            print(f"🏆 Best Model: {result['name']}")
            print(f"📁 Saved: {result['model_path']}")
            print(f"🎯 Performance: {result['map50']*100:.2f}% mAP50")
            print(f"🧠 Knowledge Loss: {result['knowledge_loss']*100:.3f}%")
            
            if result['target_achieved'] and result['constraint_satisfied']:
                print(f"\n🏆 BREAKTHROUGH ACHIEVED! 🏆")
            else:
                print(f"\n📈 Continue optimization needed")
        else:
            print(f"\n❌ No valid models found")
            
    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
